import React, { useState } from 'react';
import Button from '../components/Button';
import StepIndicator from '../components/StepIndicator';
import { RestaurantDetails, UserDetails } from '../types';

interface RestaurantSetupStepProps {
  onComplete: (details: RestaurantDetails) => void;
  userDetails: UserDetails;
}

const RestaurantSetupStep: React.FC<RestaurantSetupStepProps> = ({ onComplete, userDetails }) => {
  const [formData, setFormData] = useState<Omit<RestaurantDetails, 'machineCode'>>({
    userId: '',
    restaurantName: '',
    restaurantAddress: '',
    restaurantType: 'Dine-In',
    location: '',
    phone: '',
    email: '',
    gstNumber: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [showDiagnostics, setShowDiagnostics] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.userId.trim()) {
      setMessage({ type: 'error', text: 'User ID is required' });
      return false;
    }
    if (formData.userId !== userDetails.userId) {
      setMessage({
        type: 'error',
        text: 'User ID does not match. Please check your email for the correct User ID.',
      });
      return false;
    }
    if (!formData.restaurantName.trim()) {
      setMessage({ type: 'error', text: 'Restaurant name is required' });
      return false;
    }
    if (!formData.restaurantAddress.trim()) {
      setMessage({ type: 'error', text: 'Restaurant address is required' });
      return false;
    }
    if (!formData.location.trim()) {
      setMessage({ type: 'error', text: 'Location is required' });
      return false;
    }
    return true;
  };

  const handleDiagnostics = async () => {
    try {
      const diagnostics = await window.electronAPI.databaseDiagnostics();
      console.log('Database Diagnostics:', diagnostics);
      alert(`Database Diagnostics:\n${JSON.stringify(diagnostics, null, 2)}`);
    } catch (error) {
      console.error('Failed to run diagnostics:', error);
      alert('Failed to run database diagnostics');
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Generate machine code
      const machineCodeResult = await window.electronAPI.generateMachineCode();

      const restaurantDetailsWithMachineCode: RestaurantDetails = {
        ...formData,
        machineCode: machineCodeResult.machineCode,
      };

      // Add a small delay to ensure user data is committed to database
      await new Promise(resolve => setTimeout(resolve, 500));

      // Store restaurant data
      const storeResult = await window.electronAPI.storeRestaurantData(
        restaurantDetailsWithMachineCode
      );

      if (storeResult.success) {
        setMessage({
          type: 'success',
          text: 'Restaurant setup completed successfully!',
        });

        // Wait a moment to show the success message
        setTimeout(() => {
          onComplete(restaurantDetailsWithMachineCode);
        }, 1500);
      } else {
        // Enhanced error reporting
        let errorMessage = 'Failed to save restaurant data. ';
        if (storeResult.error) {
          errorMessage += `Error: ${storeResult.error}`;
        } else {
          errorMessage += 'Please try again.';
        }

        setMessage({
          type: 'error',
          text: errorMessage,
        });

        // Log detailed error for debugging
        console.error('Restaurant data save failed:', {
          result: storeResult,
          formData: restaurantDetailsWithMachineCode
        });
      }
    } catch (error) {
      console.error('Error in restaurant setup step:', error);
      setMessage({
        type: 'error',
        text: 'An error occurred. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="onboarding-container">
      <StepIndicator currentStep={1} totalSteps={3} />

      <h1 className="onboarding-title">Restaurant Setup</h1>
      <p className="onboarding-subtitle">Enter your User ID and restaurant details</p>

      {message && <div className={`message message-${message.type}`}>{message.text}</div>}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label className="form-label" htmlFor="userId">
            User ID
          </label>
          <input
            type="text"
            id="userId"
            name="userId"
            className="form-input"
            value={formData.userId}
            onChange={handleInputChange}
            placeholder="Enter the User ID from your email"
            required
          />
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="restaurantName">
            Restaurant Name
          </label>
          <input
            type="text"
            id="restaurantName"
            name="restaurantName"
            className="form-input"
            value={formData.restaurantName}
            onChange={handleInputChange}
            placeholder="Enter your restaurant name"
            required
          />
          <div className="form-note">
            <span className="note-icon">ℹ️</span>
            Add restaurant name as per license
          </div>
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="restaurantAddress">
            Restaurant Address
          </label>
          <input
            type="text"
            id="restaurantAddress"
            name="restaurantAddress"
            className="form-input"
            value={formData.restaurantAddress}
            onChange={handleInputChange}
            placeholder="Enter your restaurant address"
            required
          />
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="restaurantType">
            Restaurant Type
          </label>
          <select
            id="restaurantType"
            name="restaurantType"
            className="form-select"
            value={formData.restaurantType}
            onChange={handleInputChange}
            required
          >
            <option value="Dine-In">Dine-In (Full service with table management)</option>
            <option value="Takeaway">Takeaway (Takeaway and delivery only)</option>
          </select>
          <small className="form-help">
            {formData.restaurantType === 'Dine-In'
              ? 'All modules including table management, order management, and POS will be available.'
              : 'Only takeaway and delivery modules will be available. Table management will be hidden.'}
          </small>
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="location">
            Location
          </label>
          <input
            type="text"
            id="location"
            name="location"
            className="form-input"
            value={formData.location}
            onChange={handleInputChange}
            placeholder="Enter your location (city, area)"
            required
          />
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="phone">
            Phone Number
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            className="form-input"
            value={formData.phone}
            onChange={handleInputChange}
            placeholder="Enter restaurant phone number"
          />
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="email">
            Email Address
          </label>
          <input
            type="email"
            id="email"
            name="email"
            className="form-input"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Enter restaurant email address"
          />
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="gstNumber">
            GST Number
          </label>
          <input
            type="text"
            id="gstNumber"
            name="gstNumber"
            className="form-input"
            value={formData.gstNumber}
            onChange={handleInputChange}
            placeholder="Enter GST number (optional)"
          />
          <div className="form-note">
            <span className="note-icon">ℹ️</span>
            Add GST number as per license (if applicable)
          </div>
          <small className="form-help">
            GST number will be used in billing and receipts if provided.
          </small>
        </div>

        <div className="button-group">
          <Button type="submit" variant="primary" isLoading={isLoading} disabled={isLoading}>
            {isLoading ? 'Setting up...' : 'Next'}
          </Button>

          {process.env.NODE_ENV === 'development' && (
            <div style={{ marginLeft: '10px', display: 'inline-block' }}>
              <Button
                type="button"
                variant="secondary"
                onClick={handleDiagnostics}
                disabled={isLoading}
              >
                Run Diagnostics
              </Button>
            </div>
          )}
        </div>
      </form>
    </div>
  );
};

export default RestaurantSetupStep;
