import React, { useState, useEffect } from 'react';
import UserDetailsStep from './pages/UserDetailsStep';
import RestaurantSetupStep from './pages/RestaurantSetupStep';
import PinLoginStep from './pages/PinLoginStep';
import Dashboard from './pages/Dashboard';
import { UserDetails, RestaurantDetails } from './types';

type AppStep = 'loading' | 'user-details' | 'restaurant-setup' | 'pin-login' | 'dashboard';

const App: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<AppStep>('loading');
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const [restaurantDetails, setRestaurantDetails] = useState<RestaurantDetails | null>(null);
  const [isSetupComplete, setIsSetupComplete] = useState(false);

  // Check setup status on app load
  useEffect(() => {
    const checkSetup = async () => {
      try {
        const setupStatus = await window.electronAPI.checkSetupStatus();
        setIsSetupComplete(setupStatus.isSetupComplete);

        if (setupStatus.isSetupComplete) {
          // Load existing user and restaurant data
          const userData = await window.electronAPI.getUserData();
          const restaurantData = await window.electronAPI.getRestaurantData();

          if (userData) {
            setUserDetails(userData);
          }
          if (restaurantData) {
            setRestaurantDetails(restaurantData);
          }

          // Go directly to PIN login for existing setup
          setCurrentStep('pin-login');
        } else {
          // Start onboarding flow
          setCurrentStep('user-details');
        }
      } catch (error) {
        console.error('Error checking setup status:', error);
        setCurrentStep('user-details');
      }
    };

    checkSetup();
  }, []);

  const handleUserDetailsComplete = (details: UserDetails) => {
    setUserDetails(details);
    setCurrentStep('restaurant-setup');
  };

  const handleRestaurantSetupComplete = (details: RestaurantDetails) => {
    setRestaurantDetails(details);
    setCurrentStep('pin-login');
  };

  const handlePinLoginComplete = () => {
    setCurrentStep('dashboard');
  };

  const handleLogout = () => {
    setCurrentStep('pin-login');
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'loading':
        return (
          <div className="onboarding-layout">
            <div className="onboarding-container">
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <h2 className="onboarding-title">Loading Zyka POS...</h2>
                <p className="onboarding-subtitle">
                  Please wait while we initialize the application
                </p>
              </div>
            </div>
          </div>
        );
      case 'user-details':
        return (
          <div className="onboarding-layout">
            <UserDetailsStep onComplete={handleUserDetailsComplete} />
          </div>
        );
      case 'restaurant-setup':
        return (
          <div className="onboarding-layout">
            <RestaurantSetupStep
              onComplete={handleRestaurantSetupComplete}
              userDetails={userDetails!}
            />
          </div>
        );
      case 'pin-login':
        return (
          <PinLoginStep
            onComplete={handlePinLoginComplete}
            userDetails={userDetails!}
            restaurantDetails={restaurantDetails || undefined}
            isReturningUser={isSetupComplete}
          />
        );
      case 'dashboard':
        return (
          <div className="pos-layout">
            <Dashboard
              userDetails={userDetails!}
              restaurantDetails={restaurantDetails!}
              onLogout={handleLogout}
            />
          </div>
        );
      default:
        return (
          <div className="onboarding-layout">
            <UserDetailsStep onComplete={handleUserDetailsComplete} />
          </div>
        );
    }
  };

  return <div className="app">{renderCurrentStep()}</div>;
};

export default App;
