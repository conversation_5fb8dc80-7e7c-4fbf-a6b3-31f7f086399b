import React, { useState, useEffect } from 'react';
import UserDetailsStep from './pages/UserDetailsStep';
import RestaurantSetupStep from './pages/RestaurantSetupStep';
import PinLoginStep from './pages/PinLoginStep';
import Dashboard from './pages/Dashboard';
import StartupUpdateDialog from './components/StartupUpdateDialog';
import { UserDetails, RestaurantDetails } from './types';

type AppStep = 'loading' | 'checking-updates' | 'user-details' | 'restaurant-setup' | 'pin-login' | 'dashboard';

const App: React.FC = () => {
  const [currentStep, setCurrentStep] = useState<AppStep>('loading');
  const [userDetails, setUserDetails] = useState<UserDetails | null>(null);
  const [restaurantDetails, setRestaurantDetails] = useState<RestaurantDetails | null>(null);
  const [isSetupComplete, setIsSetupComplete] = useState(false);
  const [showUpdateNotification, setShowUpdateNotification] = useState(false);
  const [updateStatus, setUpdateStatus] = useState<any>(null);

  // Check for updates and setup status on app load
  useEffect(() => {
    const initializeApp = async () => {
      try {
        // First, check for updates
        setCurrentStep('checking-updates');

        // Check for updates
        const updateResult = await window.electronAPI.updaterCheckForUpdates();
        console.log('Update check result:', updateResult);

        // Wait a moment to get update status
        await new Promise(resolve => setTimeout(resolve, 2000));

        const statusResult = await window.electronAPI.updaterGetStatus();
        if (statusResult.success && statusResult.status.available) {
          setUpdateStatus(statusResult.status);
          setShowUpdateNotification(true);
          return; // Stay on update screen
        }

        // If no updates or update check failed, proceed with normal flow
        const setupStatus = await window.electronAPI.checkSetupStatus();
        setIsSetupComplete(setupStatus.isSetupComplete);

        if (setupStatus.isSetupComplete) {
          // Load existing user and restaurant data
          const userData = await window.electronAPI.getUserData();
          const restaurantData = await window.electronAPI.getRestaurantData();

          if (userData) {
            setUserDetails(userData);
          }
          if (restaurantData) {
            setRestaurantDetails(restaurantData);
          }

          // Go directly to PIN login for existing setup
          setCurrentStep('pin-login');
        } else {
          // Start onboarding flow
          setCurrentStep('user-details');
        }
      } catch (error) {
        console.error('Error during app initialization:', error);
        setCurrentStep('user-details');
      }
    };

    initializeApp();
  }, []);

  const handleUserDetailsComplete = (details: UserDetails) => {
    setUserDetails(details);
    setCurrentStep('restaurant-setup');
  };

  const handleRestaurantSetupComplete = (details: RestaurantDetails) => {
    setRestaurantDetails(details);
    setCurrentStep('pin-login');
  };

  const handlePinLoginComplete = async () => {
    try {
      // Switch to fullscreen mode for POS operation
      await window.electronAPI.switchToFullscreen();
      setCurrentStep('dashboard');
    } catch (error) {
      console.error('Error switching to fullscreen:', error);
      // Continue to dashboard even if fullscreen switch fails
      setCurrentStep('dashboard');
    }
  };

  const handleLogout = () => {
    setCurrentStep('pin-login');
  };

  const handleUpdateComplete = () => {
    setShowUpdateNotification(false);
    // Continue with normal app flow
    if (isSetupComplete) {
      setCurrentStep('pin-login');
    } else {
      setCurrentStep('user-details');
    }
  };

  const renderCurrentStep = () => {
    switch (currentStep) {
      case 'loading':
        return (
          <div className="onboarding-layout">
            <div className="onboarding-container">
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <h2 className="onboarding-title">Loading Zyka POS...</h2>
                <p className="onboarding-subtitle">
                  Please wait while we initialize the application
                </p>
              </div>
            </div>
          </div>
        );
      case 'checking-updates':
        return (
          <div className="onboarding-layout">
            <div className="onboarding-container">
              <div className="loading-container">
                <div className="loading-spinner"></div>
                <h2 className="onboarding-title">Checking for Updates...</h2>
                <p className="onboarding-subtitle">
                  Please wait while we check for the latest version
                </p>
              </div>
            </div>
          </div>
        );
      case 'user-details':
        return (
          <div className="onboarding-layout">
            <UserDetailsStep onComplete={handleUserDetailsComplete} />
          </div>
        );
      case 'restaurant-setup':
        return (
          <div className="onboarding-layout">
            <RestaurantSetupStep
              onComplete={handleRestaurantSetupComplete}
              userDetails={userDetails!}
            />
          </div>
        );
      case 'pin-login':
        return (
          <PinLoginStep
            onComplete={handlePinLoginComplete}
            userDetails={userDetails!}
            restaurantDetails={restaurantDetails || undefined}
            isReturningUser={isSetupComplete}
          />
        );
      case 'dashboard':
        return (
          <div className="pos-layout">
            <Dashboard
              userDetails={userDetails!}
              restaurantDetails={restaurantDetails!}
              onLogout={handleLogout}
            />
          </div>
        );
      default:
        return (
          <div className="onboarding-layout">
            <UserDetailsStep onComplete={handleUserDetailsComplete} />
          </div>
        );
    }
  };

  return (
    <div className="app">
      {renderCurrentStep()}
      {showUpdateNotification && (
        <StartupUpdateDialog
          onComplete={handleUpdateComplete}
          onSkip={handleUpdateComplete}
        />
      )}
    </div>
  );
};

export default App;
