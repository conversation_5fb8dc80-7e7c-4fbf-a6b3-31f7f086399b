import React, { useState, useEffect } from 'react';
import { UserDetails, RestaurantDetails } from '../types';
import SimplifiedPOS from './SimplifiedPOS';
import OrderManagement from './OrderManagement';
import POSWindowControls from './POSWindowControls';
import { MenuProvider } from '../contexts/MenuContext';
import { DashboardProvider } from '../contexts/DashboardContext';
import { NotificationProvider } from '../contexts/NotificationContext';
import ToastContainer from './notifications/ToastContainer';
import { eventBus, EVENTS } from '../utils/eventBus';
import Icon from './Icon';

interface DedicatedPOSWindowProps {
  windowId: string;
  restaurantId: string;
  userDetails: UserDetails;
  restaurantDetails: RestaurantDetails;
}

type POSView = 'pos' | 'orders';

const DedicatedPOSWindow: React.FC<DedicatedPOSWindowProps> = ({
  windowId,
  restaurantId,
  userDetails,
  restaurantDetails,
}) => {
  const [isLoading, setIsLoading] = useState(true);
  const [hasActiveOrder, setHasActiveOrder] = useState(false);
  const [orderItemCount, setOrderItemCount] = useState(0);
  const [currentView, setCurrentView] = useState<POSView>('pos');

  useEffect(() => {
    // Initialize the POS window
    const initializePOSWindow = async () => {
      try {
        // Set up communication with main application
        if (window.electronAPI && (window.electronAPI as any).onPOSMessage) {
          (window.electronAPI as any).onPOSMessage((message: any) => {
            handleMainAppMessage(message);
          });
        }

        if (window.electronAPI && (window.electronAPI as any).onPOSBroadcast) {
          (window.electronAPI as any).onPOSBroadcast((message: any) => {
            handleBroadcastMessage(message);
          });
        }

        // Notify main application that POS window is ready
        if (window.electronAPI && (window.electronAPI as any).sendPOSMessage) {
          (window.electronAPI as any).sendPOSMessage(windowId, {
            type: 'POS_WINDOW_READY',
            windowId,
            restaurantId,
          });
        }

        setIsLoading(false);
      } catch (error) {
        console.error('Failed to initialize POS window:', error);
        setIsLoading(false);
      }
    };

    initializePOSWindow();

    // Cleanup on unmount
    return () => {
      if (window.electronAPI && (window.electronAPI as any).removePOSListeners) {
        (window.electronAPI as any).removePOSListeners();
      }
    };
  }, [windowId, restaurantId]);

  const handleMainAppMessage = (message: any) => {
    switch (message.type) {
      case 'MENU_UPDATED':
        // Refresh menu data
        eventBus.emit(EVENTS.MENU_UPDATED);
        break;
      case 'TABLE_UPDATED':
        // Refresh table data
        eventBus.emit(EVENTS.TABLE_UPDATED);
        break;
      case 'ORDER_UPDATED':
        // Handle order updates
        eventBus.emit(EVENTS.ORDER_UPDATED, message.data);
        break;
      default:
        console.log('Received unknown message type:', message.type);
    }
  };

  const handleBroadcastMessage = (message: any) => {
    switch (message.type) {
      case 'GLOBAL_MENU_UPDATE':
        eventBus.emit(EVENTS.MENU_UPDATED);
        break;
      case 'GLOBAL_TABLE_UPDATE':
        eventBus.emit(EVENTS.TABLE_UPDATED);
        break;
      case 'SYSTEM_NOTIFICATION':
        // Handle system-wide notifications
        console.log('System notification:', message.data);
        break;
      default:
        console.log('Received unknown broadcast:', message.type);
    }
  };

  // Send updates back to main application
  const notifyMainApp = (type: string, data?: any) => {
    if (window.electronAPI && (window.electronAPI as any).broadcastToPOSWindows) {
      (window.electronAPI as any).broadcastToPOSWindows({
        type,
        data,
        sourceWindowId: windowId,
      });
    }
  };

  // Listen for events that should be communicated back to main app
  useEffect(() => {
    const handleOrderCreated = (orderData: any) => {
      notifyMainApp('ORDER_CREATED', orderData);
      setHasActiveOrder(false); // Clear active order state
      // Navigate to orders view instead of closing window
      setCurrentView('orders');
    };

    const handleTableUpdated = (tableData: any) => {
      notifyMainApp('TABLE_UPDATED', tableData);
    };

    // Listen for order state changes
    const handleOrderItemAdded = (data: any) => {
      setHasActiveOrder(true);
      // Update item count - this is a simple increment, could be more sophisticated
      setOrderItemCount(prev => prev + 1);
    };

    const handleOrderCleared = () => {
      setHasActiveOrder(false);
      setOrderItemCount(0);
    };

    const handleNavigateToOrders = () => {
      setCurrentView('orders');
    };

    eventBus.on(EVENTS.ORDER_CREATED, handleOrderCreated);
    eventBus.on(EVENTS.TABLE_UPDATED, handleTableUpdated);
    eventBus.on('ORDER_ITEM_ADDED', handleOrderItemAdded);
    eventBus.on('ORDER_CLEARED', handleOrderCleared);
    eventBus.on('NAVIGATE_TO_ORDERS', handleNavigateToOrders);

    return () => {
      eventBus.off(EVENTS.ORDER_CREATED, handleOrderCreated);
      eventBus.off(EVENTS.TABLE_UPDATED, handleTableUpdated);
      eventBus.off('ORDER_ITEM_ADDED', handleOrderItemAdded);
      eventBus.off('ORDER_CLEARED', handleOrderCleared);
      eventBus.off('NAVIGATE_TO_ORDERS', handleNavigateToOrders);
    };
  }, [windowId]);

  if (isLoading) {
    return (
      <div className="pos-loading-container">
        <div className="pos-loading-spinner"></div>
        <p className="pos-loading-text">Initializing Order Taking System...</p>
      </div>
    );
  }

  const renderContent = () => {
    switch (currentView) {
      case 'pos':
        return <SimplifiedPOS restaurantId={restaurantId} />;
      case 'orders':
        return <OrderManagement restaurantId={restaurantId} />;
      default:
        return <SimplifiedPOS restaurantId={restaurantId} />;
    }
  };

  return (
    <NotificationProvider userId={userDetails.userId || restaurantDetails.userId}>
      <DashboardProvider restaurantId={restaurantDetails.userId}>
        <MenuProvider restaurantId={restaurantDetails.userId}>
          <div className="dedicated-pos-window">
            <POSWindowControls hasActiveOrder={hasActiveOrder} orderItemCount={orderItemCount} />

            {/* Navigation Bar */}
            <div className="pos-navigation">
              <button
                className={`nav-btn ${currentView === 'pos' ? 'active' : ''}`}
                onClick={() => setCurrentView('pos')}
              >
                <Icon name="plus" size="sm" />
                New Order
              </button>
              <button
                className={`nav-btn ${currentView === 'orders' ? 'active' : ''}`}
                onClick={() => setCurrentView('orders')}
              >
                <Icon name="list" size="sm" />
                Order History
              </button>
            </div>

            <div className="pos-content-area">{renderContent()}</div>
            <ToastContainer />
          </div>
        </MenuProvider>
      </DashboardProvider>
    </NotificationProvider>
  );
};

export default DedicatedPOSWindow;
