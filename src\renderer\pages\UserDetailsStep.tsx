import React, { useState } from 'react';
import Button from '../components/Button';
import StepIndicator from '../components/StepIndicator';
import { UserDetails } from '../types';

interface UserDetailsStepProps {
  onComplete: (details: UserDetails) => void;
}

const UserDetailsStep: React.FC<UserDetailsStepProps> = ({ onComplete }) => {
  const [formData, setFormData] = useState<UserDetails>({
    fullName: '',
    address: '',
    phone: '',
    email: '',
  });
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState<{ type: 'success' | 'error'; text: string } | null>(null);
  const [isTestingEmail, setIsTestingEmail] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const validateForm = (): boolean => {
    if (!formData.fullName.trim()) {
      setMessage({ type: 'error', text: 'Full name is required' });
      return false;
    }
    if (!formData.address.trim()) {
      setMessage({ type: 'error', text: 'Address is required' });
      return false;
    }
    if (!formData.phone.trim()) {
      setMessage({ type: 'error', text: 'Phone number is required' });
      return false;
    }
    if (!formData.email.trim()) {
      setMessage({ type: 'error', text: 'Email is required' });
      return false;
    }
    if (!/\S+@\S+\.\S+/.test(formData.email)) {
      setMessage({ type: 'error', text: 'Please enter a valid email address' });
      return false;
    }
    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setMessage(null);

    if (!validateForm()) {
      return;
    }

    setIsLoading(true);

    try {
      // Generate user credentials
      const credentials = await window.electronAPI.generateUserCredentials();

      const userDetailsWithCredentials: UserDetails = {
        ...formData,
        userId: credentials.userId,
        pin: credentials.pin,
      };

      // Store user data
      await window.electronAPI.storeUserData(userDetailsWithCredentials);

      // Send email with credentials
      const emailResult = await window.electronAPI.sendEmail({
        email: formData.email,
        userId: credentials.userId,
        pin: credentials.pin,
      });

      if (emailResult.success) {
        setMessage({
          type: 'success',
          text: 'Credentials sent to your email! Please check your inbox.',
        });

        // Wait a moment to show the success message
        setTimeout(() => {
          onComplete(userDetailsWithCredentials);
        }, 2000);
      } else {
        // Even if email fails, continue with the process
        setMessage({
          type: 'error',
          text: 'Email sending failed, but you can continue. Your credentials will be displayed in the next step.',
        });

        setTimeout(() => {
          onComplete(userDetailsWithCredentials);
        }, 3000);
      }
    } catch (error) {
      console.error('Error in user details step:', error);
      setMessage({
        type: 'error',
        text: 'An error occurred. Please try again.',
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="onboarding-container">
      <StepIndicator currentStep={0} totalSteps={3} />

      <h1 className="onboarding-title">Welcome to Zyka POS</h1>
      <p className="onboarding-subtitle">Let's start by collecting your details</p>

      {message && <div className={`message message-${message.type}`}>{message.text}</div>}

      <form onSubmit={handleSubmit}>
        <div className="form-group">
          <label className="form-label" htmlFor="fullName">
            Full Name
          </label>
          <input
            type="text"
            id="fullName"
            name="fullName"
            className="form-input"
            value={formData.fullName}
            onChange={handleInputChange}
            placeholder="Enter your full name"
            required
          />
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="address">
            Address
          </label>
          <input
            type="text"
            id="address"
            name="address"
            className="form-input"
            value={formData.address}
            onChange={handleInputChange}
            placeholder="Enter your address"
            required
          />
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="phone">
            Phone Number
          </label>
          <input
            type="tel"
            id="phone"
            name="phone"
            className="form-input"
            value={formData.phone}
            onChange={handleInputChange}
            placeholder="Enter your phone number"
            required
          />
        </div>

        <div className="form-group">
          <label className="form-label" htmlFor="email">
            Email Address
          </label>
          <input
            type="email"
            id="email"
            name="email"
            className="form-input"
            value={formData.email}
            onChange={handleInputChange}
            placeholder="Enter your email address"
            required
          />
        </div>

        <Button type="submit" variant="primary" isLoading={isLoading} disabled={isLoading}>
          {isLoading ? 'Processing...' : 'Next'}
        </Button>
      </form>
    </div>
  );
};

export default UserDetailsStep;
