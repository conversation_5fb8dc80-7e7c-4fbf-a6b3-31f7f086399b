import { app, BrowserWindow, ipcMain } from 'electron';
import * as path from 'path';
import * as nodemailer from 'nodemailer';
import * as dotenv from 'dotenv';
import { AnalyticsService } from './services/analyticsService';
import { getConfigService } from './services/configService';
import { getDatabaseService } from './services/databaseService';
import { getBackupService } from './services/backupService';
import { getAutoUpdaterService } from './services/autoUpdaterService';
import { getEnvironmentService } from './services/environmentService';
import { setupAllIpcHandlers } from './services/ipcHandlers';
import { DatabaseDiagnostics } from './services/databaseDiagnostics';

// Load environment variables
dotenv.config();

let mainWindow: BrowserWindow;

// POS Window Management
let posWindows: Map<string, BrowserWindow> = new Map();
let posWindowCounter = 0;

// Initialize services
const environmentService = getEnvironmentService();
const databaseService = getDatabaseService();
const backupService = getBackupService();
const autoUpdaterService = getAutoUpdaterService();
let analyticsService: AnalyticsService | null = null;
const configService = getConfigService();

// Initialize database and services
async function initializeDatabase(): Promise<void> {
  try {
    console.log('Running database diagnostics...');
    await DatabaseDiagnostics.logDiagnostics();

    console.log('Initializing environment service...');
    await environmentService.initialize();
    console.log('Environment service initialized successfully');

    console.log('Initializing SQLite database...');
    await databaseService.initialize();
    console.log('SQLite database initialized successfully');

    console.log('Initializing backup service...');
    await backupService.initialize();
    console.log('Backup service initialized successfully');

    console.log('Setting up IPC handlers...');
    setupAllIpcHandlers();
    console.log('IPC handlers set up successfully');

    console.log('Database and services initialized successfully');
  } catch (error) {
    console.error('Failed to initialize services:', error);
    console.error('Error details:', {
      message: (error as Error).message,
      stack: (error as Error).stack
    });
    throw error;
  }
}

// Initialize analytics service
async function initializeAnalytics(): Promise<void> {
  try {
    await configService.loadConfig();
    const analyticsConfig = configService.getAnalyticsConfig();

    if (analyticsConfig.enabled) {
      // Note: Analytics service will need to be updated to work with SQLite
      // analyticsService = new AnalyticsService(databaseService, analyticsConfig);
      // await analyticsService.initialize();
      console.log('Analytics service will be initialized after SQLite integration');
    } else {
      console.log('Analytics service disabled in configuration');
    }
  } catch (error) {
    console.error('Failed to initialize analytics service:', error);
  }
}

function createWindow(): void {
  // Create the browser window with medium-sized configuration for setup wizard
  // Will switch to fullscreen after setup is complete
  mainWindow = new BrowserWindow({
    width: 700,
    height: 600,
    minWidth: 600,
    minHeight: 500,
    maxWidth: 1200,
    maxHeight: 900,
    center: true,
    resizable: true,
    maximizable: true,
    minimizable: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false
    },
    title: 'Zyka POS - Setup Wizard',
    frame: true,
    show: false,
    icon: path.join(__dirname, '../assets/icon.png')
  });

  // Load the app
  if (process.env.NODE_ENV === 'development') {
    mainWindow.loadFile(path.join(__dirname, 'index.html'));
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, 'index.html'));
  }

  mainWindow.once('ready-to-show', () => {
    mainWindow.show();

    // Initialize auto-updater after window is ready
    autoUpdaterService.initialize(mainWindow);
  });

  mainWindow.on('closed', () => {
    mainWindow = null as any;
  });
}

// App event handlers
app.whenReady().then(async () => {
  try {
    await initializeDatabase();
    await initializeAnalytics();
    createWindow();
  } catch (error) {
    console.error('Failed to initialize application:', error);
    app.quit();
  }

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) createWindow();
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') app.quit();
});

app.on('before-quit', async () => {
  // Cleanup services
  if (analyticsService) {
    // await analyticsService.stop();
  }
  if (backupService && typeof backupService.stop === 'function') {
    backupService.stop();
  }
  if (autoUpdaterService) {
    autoUpdaterService.stop();
  }
});

// Window control handlers
ipcMain.handle('minimize-window', () => {
  if (mainWindow) {
    mainWindow.minimize();
  }
});

ipcMain.handle('maximize-window', () => {
  if (mainWindow) {
    if (mainWindow.isMaximized()) {
      mainWindow.unmaximize();
    } else {
      mainWindow.maximize();
    }
  }
});

ipcMain.handle('close-window', () => {
  if (mainWindow) {
    mainWindow.close();
  }
});

// Close specific POS window
ipcMain.handle('close-pos-window-self', (event) => {
  const senderWindow = BrowserWindow.fromWebContents(event.sender);
  if (senderWindow && senderWindow !== mainWindow) {
    // Find and remove from posWindows map
    for (const [windowId, window] of posWindows.entries()) {
      if (window === senderWindow) {
        posWindows.delete(windowId);
        break;
      }
    }
    senderWindow.close();
  }
});

// POS Window Management Functions
function createPOSWindow(restaurantId: string, userDetails: any, restaurantDetails: any): string {
  const windowId = `pos_${++posWindowCounter}_${Date.now()}`;

  const posWindow = new BrowserWindow({
    width: 1400,
    height: 900,
    minWidth: 1024,
    minHeight: 768,
    center: true,
    resizable: true,
    maximizable: true,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      preload: path.join(__dirname, 'preload.js'),
      webSecurity: false,
      additionalArguments: [
        `--pos-window-id=${windowId}`,
        `--restaurant-id=${restaurantId}`,
        `--user-details=${JSON.stringify(userDetails)}`,
        `--restaurant-details=${JSON.stringify(restaurantDetails)}`
      ]
    },
    titleBarStyle: 'hidden',
    frame: false,
    show: false,
    icon: path.join(__dirname, '../assets/icon.png'),
    parent: mainWindow
  });

  // Load the POS window content with parameters
  const posWindowUrl = `file://${path.join(__dirname, 'pos-window.html')}?windowId=${windowId}&restaurantId=${restaurantId}&userDetails=${encodeURIComponent(JSON.stringify(userDetails))}&restaurantDetails=${encodeURIComponent(JSON.stringify(restaurantDetails))}`;

  posWindow.loadURL(posWindowUrl);

  posWindow.once('ready-to-show', () => {
    posWindow.show();
    posWindow.focus();
  });

  posWindow.on('closed', () => {
    posWindows.delete(windowId);
  });

  posWindows.set(windowId, posWindow);
  return windowId;
}

// POS Window IPC Handlers
ipcMain.handle('create-pos-window', (_, restaurantId: string, userDetails: any, restaurantDetails: any) => {
  try {
    const windowId = createPOSWindow(restaurantId, userDetails, restaurantDetails);
    return { success: true, windowId };
  } catch (error) {
    console.error('Failed to create POS window:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('close-pos-window', (_, windowId: string) => {
  try {
    const posWindow = posWindows.get(windowId);
    if (posWindow) {
      posWindow.close();
      return { success: true };
    }
    return { success: false, error: 'Window not found' };
  } catch (error) {
    console.error('Failed to close POS window:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('get-pos-windows', () => {
  return Array.from(posWindows.keys());
});

// POS Window Communication
ipcMain.handle('pos-window-message', (_, windowId: string, message: any) => {
  try {
    const posWindow = posWindows.get(windowId);
    if (posWindow) {
      posWindow.webContents.send('pos-message', message);
      return { success: true };
    }
    return { success: false, error: 'Window not found' };
  } catch (error) {
    console.error('Failed to send message to POS window:', error);
    return { success: false, error: (error as Error).message };
  }
});

// Broadcast to all POS windows
ipcMain.handle('broadcast-to-pos-windows', (_, message: any) => {
  try {
    posWindows.forEach((window) => {
      window.webContents.send('pos-broadcast', message);
    });
    return { success: true };
  } catch (error) {
    console.error('Failed to broadcast to POS windows:', error);
    return { success: false, error: (error as Error).message };
  }
});

// Email helper functions
async function sendTrialStartedEmail(email: string, fullName: string, trialStartDate: Date, trialEndDate: Date): Promise<void> {
  const emailConfig = environmentService.getEmailConfig();

  if (!environmentService.isEmailConfigured()) {
    throw new Error('Email credentials not configured');
  }

  const transporter = nodemailer.createTransport({
    service: emailConfig.service,
    auth: {
      user: emailConfig.user,
      pass: emailConfig.pass
    }
  });

  const trialDays = Math.ceil((trialEndDate.getTime() - trialStartDate.getTime()) / (1000 * 60 * 60 * 24));

  const mailOptions = {
    from: `"Zyka POS" <${emailConfig.user}>`,
    to: email,
    subject: '🎉 Your Zyka POS Trial Has Started!',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Trial Started - Zyka POS</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f5f5f5;">
        <div style="max-width: 600px; margin: 0 auto; background-color: white; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
          <!-- Header -->
          <div style="background: linear-gradient(135deg, #10b981 0%, #059669 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 300;">🎉 Trial Started!</h1>
            <p style="color: rgba(255, 255, 255, 0.9); margin: 8px 0 0 0; font-size: 16px;">Your Zyka POS journey begins now</p>
          </div>

          <!-- Content -->
          <div style="padding: 40px 30px;">
            <h2 style="color: #2d3748; margin: 0 0 16px 0; font-size: 24px; font-weight: 600;">Welcome ${fullName}! 🚀</h2>

            <p style="color: #4a5568; margin: 0 0 24px 0; font-size: 16px; line-height: 1.6;">
              Congratulations! Your ${trialDays}-day free trial of Zyka POS has officially started. You now have full access to all premium features.
            </p>

            <!-- Trial Info Box -->
            <div style="background: #f0fdf4; border: 2px solid #10b981; border-radius: 12px; padding: 24px; margin: 24px 0;">
              <h3 style="color: #065f46; margin: 0 0 16px 0; font-size: 18px; font-weight: 600;">📅 Trial Details</h3>

              <div style="margin-bottom: 12px;">
                <p style="color: #047857; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">Trial Started</p>
                <p style="color: #065f46; margin: 0; font-size: 16px; font-weight: 600;">${trialStartDate.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
              </div>

              <div style="margin-bottom: 12px;">
                <p style="color: #047857; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">Trial Expires</p>
                <p style="color: #065f46; margin: 0; font-size: 16px; font-weight: 600;">${trialEndDate.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}</p>
              </div>

              <div>
                <p style="color: #047857; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">Days Remaining</p>
                <p style="color: #065f46; margin: 0; font-size: 20px; font-weight: 700;">${trialDays} Days</p>
              </div>
            </div>

            <h3 style="color: #2d3748; margin: 32px 0 16px 0; font-size: 18px; font-weight: 600;">🌟 What You Get During Trial</h3>

            <ul style="color: #4a5568; margin: 0; padding-left: 20px; line-height: 1.8;">
              <li><strong>Complete POS System</strong> - Full point-of-sale functionality</li>
              <li><strong>Menu Management</strong> - Add unlimited menu items and categories</li>
              <li><strong>Table Management</strong> - Manage up to 100+ tables</li>
              <li><strong>Order Processing</strong> - Handle dine-in, takeaway, and delivery orders</li>
              <li><strong>Analytics & Reports</strong> - Detailed business insights</li>
              <li><strong>Cloud Backup</strong> - Secure data backup and sync</li>
              <li><strong>Email Support</strong> - Get help when you need it</li>
            </ul>

            <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 16px; margin: 24px 0;">
              <h4 style="color: #92400e; margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">💡 Pro Tip</h4>
              <p style="color: #92400e; margin: 0; font-size: 14px; line-height: 1.6;">
                Make the most of your trial! Set up your restaurant profile, add your menu items, and explore all features.
                Need help? Reply to this email or contact our support team.
              </p>
            </div>
          </div>

          <!-- Footer -->
          <div style="background: #f7fafc; padding: 24px 30px; border-top: 1px solid #e2e8f0; text-align: center;">
            <p style="color: #718096; margin: 0 0 8px 0; font-size: 14px;">
              Questions? Contact us at
              <a href="mailto:<EMAIL>" style="color: #10b981; text-decoration: none;"><EMAIL></a>
            </p>
            <p style="color: #a0aec0; margin: 0; font-size: 12px;">
              © 2024 Zyka POS by Eria Software. All rights reserved.
            </p>
          </div>
        </div>
      </body>
      </html>
    `
  };

  await transporter.sendMail(mailOptions);
}

async function sendAdminNotificationEmail(userData: any, trialStartDate: Date, trialEndDate: Date): Promise<void> {
  const emailConfig = environmentService.getEmailConfig();

  if (!environmentService.isEmailConfigured()) {
    throw new Error('Email credentials not configured');
  }

  const transporter = nodemailer.createTransport({
    service: emailConfig.service,
    auth: {
      user: emailConfig.user,
      pass: emailConfig.pass
    }
  });

  const mailOptions = {
    from: `"Zyka POS" <${emailConfig.user}>`,
    to: '<EMAIL>',
    subject: '🆕 New User Registration - Zyka POS',
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New User Registration</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f5f5f5;">
        <div style="max-width: 600px; margin: 0 auto; background-color: white; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
          <!-- Header -->
          <div style="background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%); padding: 30px; text-align: center;">
            <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 300;">🆕 New User Alert</h1>
            <p style="color: rgba(255, 255, 255, 0.9); margin: 8px 0 0 0; font-size: 16px;">Zyka POS Registration</p>
          </div>

          <!-- Content -->
          <div style="padding: 40px 30px;">
            <h2 style="color: #2d3748; margin: 0 0 16px 0; font-size: 24px; font-weight: 600;">New User Registered</h2>

            <p style="color: #4a5568; margin: 0 0 24px 0; font-size: 16px; line-height: 1.6;">
              A new user has registered for Zyka POS and started their trial period. Here are the details:
            </p>

            <!-- User Details Box -->
            <div style="background: #f8fafc; border: 2px solid #e2e8f0; border-radius: 12px; padding: 24px; margin: 24px 0;">
              <h3 style="color: #2d3748; margin: 0 0 16px 0; font-size: 18px; font-weight: 600;">👤 User Information</h3>

              <div style="display: grid; gap: 12px;">
                <div>
                  <p style="color: #718096; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">Full Name</p>
                  <p style="color: #2d3748; margin: 0; font-size: 16px; font-weight: 600;">${userData.fullName}</p>
                </div>

                <div>
                  <p style="color: #718096; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">Email</p>
                  <p style="color: #2d3748; margin: 0; font-size: 16px; font-weight: 600;">${userData.email}</p>
                </div>

                <div>
                  <p style="color: #718096; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">Phone</p>
                  <p style="color: #2d3748; margin: 0; font-size: 16px; font-weight: 600;">${userData.phone}</p>
                </div>

                <div>
                  <p style="color: #718096; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">Address</p>
                  <p style="color: #2d3748; margin: 0; font-size: 16px; font-weight: 600;">${userData.address}</p>
                </div>

                <div>
                  <p style="color: #718096; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">User ID</p>
                  <p style="color: #2d3748; margin: 0; font-size: 16px; font-weight: 600; font-family: 'Courier New', monospace;">${userData.userId}</p>
                </div>
              </div>
            </div>

            <!-- Trial Details Box -->
            <div style="background: #f0fdf4; border: 2px solid #10b981; border-radius: 12px; padding: 24px; margin: 24px 0;">
              <h3 style="color: #065f46; margin: 0 0 16px 0; font-size: 18px; font-weight: 600;">📅 Trial Information</h3>

              <div style="display: grid; gap: 12px;">
                <div>
                  <p style="color: #047857; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">Trial Started</p>
                  <p style="color: #065f46; margin: 0; font-size: 16px; font-weight: 600;">${trialStartDate.toLocaleString()}</p>
                </div>

                <div>
                  <p style="color: #047857; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">Trial Expires</p>
                  <p style="color: #065f46; margin: 0; font-size: 16px; font-weight: 600;">${trialEndDate.toLocaleString()}</p>
                </div>

                <div>
                  <p style="color: #047857; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">Trial Duration</p>
                  <p style="color: #065f46; margin: 0; font-size: 16px; font-weight: 600;">14 Days</p>
                </div>

                <div>
                  <p style="color: #047857; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">Status</p>
                  <p style="color: #065f46; margin: 0; font-size: 16px; font-weight: 600;">Active Trial</p>
                </div>
              </div>
            </div>

            <div style="background: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 16px; margin: 24px 0;">
              <h4 style="color: #92400e; margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">📊 Action Required</h4>
              <p style="color: #92400e; margin: 0; font-size: 14px; line-height: 1.6;">
                Monitor this user's trial progress and be ready to follow up before the trial expires.
                Consider sending a conversion email 3 days before trial expiration.
              </p>
            </div>
          </div>

          <!-- Footer -->
          <div style="background: #f7fafc; padding: 24px 30px; border-top: 1px solid #e2e8f0; text-align: center;">
            <p style="color: #718096; margin: 0 0 8px 0; font-size: 14px;">
              This is an automated notification from Zyka POS System
            </p>
            <p style="color: #a0aec0; margin: 0; font-size: 12px;">
              © 2024 Zyka POS by Eria Software. All rights reserved.
            </p>
          </div>
        </div>
      </body>
      </html>
    `
  };

  await transporter.sendMail(mailOptions);
}

// Basic IPC handlers for setup and authentication
ipcMain.handle('generate-user-credentials', async () => {
  try {
    // Generate unique 5-digit User ID
    let userId: string;
    let isUnique = false;
    let attempts = 0;
    const maxAttempts = 100;

    do {
      // Generate random 5-digit number (10000-99999)
      userId = Math.floor(10000 + Math.random() * 90000).toString();

      // Check if this ID already exists
      const existingUser = await databaseService.sqliteService.get(`
        SELECT user_id FROM users WHERE user_id = ?
      `, [userId]);

      isUnique = !existingUser;
      attempts++;

      if (attempts >= maxAttempts) {
        throw new Error('Unable to generate unique user ID after maximum attempts');
      }
    } while (!isUnique);

    const pin = Math.floor(100000 + Math.random() * 900000).toString(); // 6-digit PIN
    return { userId, pin };
  } catch (error) {
    console.error('Error generating user credentials:', error);
    // Fallback to timestamp-based ID if random generation fails
    const fallbackId = Date.now().toString().slice(-5);
    return { userId: fallbackId, pin: '123456' };
  }
});

ipcMain.handle('send-email', async (_, { email, userId, pin }) => {
  try {
    console.log('Attempting to send email to:', email);
    console.log('User ID:', userId);
    console.log('PIN:', pin);

    // Check if email credentials are configured
    if (!environmentService.isEmailConfigured()) {
      console.log('Email credentials not configured, skipping email send');
      return {
        success: false,
        error: 'Email credentials not configured. Please configure email settings in the application.'
      };
    }

    const emailConfig = environmentService.getEmailConfig();

    // Create transporter
    const transporter = nodemailer.createTransport({
      service: emailConfig.service,
      auth: {
        user: emailConfig.user,
        pass: emailConfig.pass
      }
    });

    // Email content
    const mailOptions = {
      from: `"Zyka POS" <${emailConfig.user}>`,
      to: email,
      subject: 'Welcome to Zyka POS - Your Login Credentials',
      html: `
        <!DOCTYPE html>
        <html>
        <head>
          <meta charset="utf-8">
          <meta name="viewport" content="width=device-width, initial-scale=1.0">
          <title>Welcome to Zyka POS</title>
        </head>
        <body style="margin: 0; padding: 0; font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background-color: #f5f5f5;">
          <div style="max-width: 600px; margin: 0 auto; background-color: white; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
            <!-- Header -->
            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); padding: 30px; text-align: center;">
              <h1 style="color: white; margin: 0; font-size: 28px; font-weight: 300;">Zyka POS</h1>
              <p style="color: rgba(255, 255, 255, 0.9); margin: 8px 0 0 0; font-size: 16px;">Restaurant Management System</p>
            </div>
            
            <!-- Content -->
            <div style="padding: 40px 30px;">
              <h2 style="color: #2d3748; margin: 0 0 16px 0; font-size: 24px; font-weight: 600;">Welcome to Zyka POS! 🎉</h2>

              <p style="color: #4a5568; margin: 0 0 24px 0; font-size: 16px; line-height: 1.6;">
                Your account has been successfully created. Below are your login credentials to access your POS system.
              </p>

              <!-- Credentials Box -->
              <div style="background: #f7fafc; border: 2px solid #e2e8f0; border-radius: 12px; padding: 24px; margin: 24px 0;">
                <h3 style="color: #2d3748; margin: 0 0 16px 0; font-size: 18px; font-weight: 600;">Your Login Credentials</h3>
                
                <div style="display: flex; justify-content: space-between; margin-bottom: 16px;">
                  <div style="flex: 1; margin-right: 16px;">
                    <p style="color: #718096; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">User ID</p>
                    <p style="color: #2d3748; margin: 0; font-size: 20px; font-weight: 700; font-family: 'Courier New', monospace; background: white; padding: 8px 12px; border-radius: 6px; border: 1px solid #e2e8f0;">${userId}</p>
                  </div>
                  
                  <div style="flex: 1;">
                    <p style="color: #718096; margin: 0 0 4px 0; font-size: 14px; font-weight: 500;">PIN</p>
                    <p style="color: #2d3748; margin: 0; font-size: 20px; font-weight: 700; font-family: 'Courier New', monospace; background: white; padding: 8px 12px; border-radius: 6px; border: 1px solid #e2e8f0;">${pin}</p>
                  </div>
                </div>
                
                <div style="background: #fef5e7; border: 1px solid #f6e05e; border-radius: 8px; padding: 12px; margin-top: 16px;">
                  <p style="color: #744210; margin: 0; font-size: 14px;">
                    <strong>⚠️ Important:</strong> Please keep these credentials secure and do not share them with unauthorized personnel.
                  </p>
                </div>
              </div>

              <h3 style="color: #2d3748; margin: 32px 0 16px 0; font-size: 18px; font-weight: 600;">Getting Started</h3>
              
              <ol style="color: #4a5568; margin: 0; padding-left: 20px; line-height: 1.8;">
                <li>Launch the Zyka POS application on your device</li>
                <li>Enter your User ID and PIN when prompted</li>
                <li>Complete the restaurant setup process</li>
                <li>Start managing your restaurant operations!</li>
              </ol>

              <div style="background: #e6fffa; border: 1px solid #38b2ac; border-radius: 8px; padding: 16px; margin: 24px 0;">
                <h4 style="color: #234e52; margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">🚀 What's Next?</h4>
                <p style="color: #234e52; margin: 0; font-size: 14px; line-height: 1.6;">
                  After logging in, you'll be guided through setting up your restaurant profile, menu items, and table configuration. 
                  Our intuitive interface will help you get started quickly!
                </p>
              </div>
            </div>
            
            <!-- Footer -->
            <div style="background: #f7fafc; padding: 24px 30px; border-top: 1px solid #e2e8f0; text-align: center;">
              <p style="color: #718096; margin: 0 0 8px 0; font-size: 14px;">
                Need help? Contact our support team at 
                <a href="mailto:<EMAIL>" style="color: #667eea; text-decoration: none;"><EMAIL></a>
              </p>
              <p style="color: #a0aec0; margin: 0; font-size: 12px;">
                © 2024 Zyka POS. All rights reserved.
              </p>
            </div>
          </div>
        </body>
        </html>
      `
    };

    // Send email
    const info = await transporter.sendMail(mailOptions);
    console.log('Email sent successfully:', info.messageId);
    
    return { success: true, messageId: info.messageId };
  } catch (error) {
    console.error('Email sending failed:', error);
    return { success: false, error: (error as Error).message };
  }
});

// Email configuration handlers
ipcMain.handle('get-email-config', async () => {
  try {
    return environmentService.getEmailConfig();
  } catch (error) {
    console.error('Failed to get email config:', error);
    return null;
  }
});

ipcMain.handle('update-email-config', async (_, emailConfig) => {
  try {
    await environmentService.updateConfig({ email: emailConfig });
    return { success: true };
  } catch (error) {
    console.error('Failed to update email config:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('test-email-config', async (_, emailConfig) => {
  try {
    const transporter = nodemailer.createTransport({
      service: emailConfig.service,
      auth: {
        user: emailConfig.user,
        pass: emailConfig.pass
      }
    });

    // Verify the connection
    await transporter.verify();

    return { success: true, message: 'Email configuration is valid and working!' };
  } catch (error) {
    console.error('Email test failed:', error);
    return { success: false, message: `Email test failed: ${(error as Error).message}` };
  }
});

// Generate machine code for restaurant identification
ipcMain.handle('generate-machine-code', async () => {
  try {
    const os = require('os');
    const crypto = require('crypto');

    // Get system information for unique machine identification
    const hostname = os.hostname();
    const platform = os.platform();
    const arch = os.arch();
    const networkInterfaces = os.networkInterfaces();

    // Get MAC address from network interfaces
    let macAddress = '';
    for (const interfaceName in networkInterfaces) {
      const interfaces = networkInterfaces[interfaceName];
      if (interfaces) {
        for (const iface of interfaces) {
          if (!iface.internal && iface.mac && iface.mac !== '00:00:00:00:00:00') {
            macAddress = iface.mac;
            break;
          }
        }
      }
      if (macAddress) break;
    }

    // Create a unique machine identifier
    const machineInfo = `${hostname}-${platform}-${arch}-${macAddress}`;
    const timestamp = Date.now().toString();

    // Generate a hash-based machine code
    const hash = crypto.createHash('sha256').update(machineInfo + timestamp).digest('hex');

    // Create a readable machine code format: MC-XXXX-XXXX-XXXX
    const machineCode = `MC-${hash.substring(0, 4).toUpperCase()}-${hash.substring(4, 8).toUpperCase()}-${hash.substring(8, 12).toUpperCase()}`;

    console.log('Generated machine code:', machineCode);
    return { machineCode };
  } catch (error) {
    console.error('Error generating machine code:', error);
    // Fallback to timestamp-based code if system info fails
    const fallbackCode = `MC-${Date.now().toString().slice(-8).toUpperCase()}`;
    return { machineCode: fallbackCode };
  }
});

// Database operations
ipcMain.handle('store-user-data', async (_, data) => {
  try {
    console.log('Storing user data:', { userId: data.userId, pin: data.pin, email: data.email, fullName: data.fullName });

    // Check if user already exists
    const existingUser = await databaseService.getUserByEmail(data.email).catch(() => null);

    if (existingUser) {
      // Update existing user
      console.log('Updating existing user:', existingUser.userId);
      await databaseService.updateUser(existingUser.userId, {
        ...data,
        pin: String(data.pin).trim(),
        subscriptionStatus: existingUser.subscriptionStatus || 'trial'
      });
      console.log('User data updated successfully');
    } else {
      // Add new user with trial period
      console.log('Creating new user with userId:', data.userId);
      const trialStartDate = new Date();
      const trialEndDate = new Date();
      trialEndDate.setDate(trialStartDate.getDate() + 14); // 14-day trial

      const newUserData = {
        ...data,
        pin: String(data.pin).trim(),
        trialStartDate: trialStartDate.toISOString(),
        trialEndDate: trialEndDate.toISOString(),
        subscriptionStatus: 'trial' as const,
        isActive: true
      };

      console.log('User data to create:', { ...newUserData, pin: '[HIDDEN]' });

      await databaseService.createUser(newUserData);

      console.log('New user created successfully');

      // Verify user was created
      const verifyUser = await databaseService.sqliteService.get(`
        SELECT user_id FROM users WHERE user_id = ? AND is_active = 1
      `, [data.userId]);
      console.log('User verification after creation:', verifyUser ? 'User found' : 'User not found');

      // Send trial started email to user
      try {
        await sendTrialStartedEmail(data.email, data.fullName, trialStartDate, trialEndDate);
        console.log('Trial started email sent to user');
      } catch (error) {
        console.error('Failed to send trial started email to user:', error);
      }

      // Send admin notification email
      try {
        await sendAdminNotificationEmail(data, trialStartDate, trialEndDate);
        console.log('Admin notification email sent');
      } catch (error) {
        console.error('Failed to send admin notification email:', error);
      }
    }

    return { success: true };
  } catch (error) {
    console.error('Database error:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('get-user-data', async () => {
  try {
    const users = await databaseService.sqliteService.all(`
      SELECT user_id, pin, full_name, address, phone, email,
             trial_start_date, trial_end_date, subscription_status,
             current_plan, subscription_end_date, is_active,
             last_login_at, created_at, updated_at
      FROM users
      WHERE is_active = 1
      ORDER BY created_at DESC
      LIMIT 1
    `);

    if (users.length === 0) return null;

    const user = users[0];
    return {
      userId: user.user_id,
      pin: user.pin,
      fullName: user.full_name,
      address: user.address,
      phone: user.phone,
      email: user.email,
      trialStartDate: user.trial_start_date,
      trialEndDate: user.trial_end_date,
      subscriptionStatus: user.subscription_status,
      currentPlan: user.current_plan,
      subscriptionEndDate: user.subscription_end_date,
      createdAt: user.created_at
    };
  } catch (error) {
    console.error('Database error:', error);
    return null;
  }
});

ipcMain.handle('validate-pin', async (_, { pin }) => {
  try {
    const trimmedPin = String(pin).trim();
    console.log('Validating PIN:', `"${trimmedPin}"`);

    const user = await databaseService.sqliteService.get(`
      SELECT user_id, pin FROM users
      WHERE pin = ? AND is_active = 1
    `, [trimmedPin]);

    const isValid = !!user;
    console.log('PIN validation result:', isValid);

    // Update last login time if valid
    if (isValid) {
      await databaseService.sqliteService.run(`
        UPDATE users
        SET last_login_at = ?, updated_at = ?
        WHERE user_id = ?
      `, [new Date().toISOString(), new Date().toISOString(), user.user_id]);
    }

    return { valid: isValid };
  } catch (error) {
    console.error('Database error:', error);
    return { valid: false };
  }
});

ipcMain.handle('store-restaurant-data', async (_, data) => {
  try {
    console.log('Storing restaurant data:', {
      userId: data.userId,
      restaurantName: data.restaurantName,
      machineCode: data.machineCode
    });

    // Enhanced database service validation
    if (!databaseService) {
      console.error('Database service instance not found');
      return { success: false, error: 'Database service instance not found' };
    }

    if (!databaseService.sqliteService) {
      console.error('SQLite service not initialized');
      return { success: false, error: 'SQLite service not initialized' };
    }

    // Check if database is actually initialized
    if (!databaseService.sqliteService.initialized) {
      console.error('Database not properly initialized');
      try {
        console.log('Attempting to reinitialize database...');
        await databaseService.initialize();
        console.log('Database reinitialized successfully');
      } catch (reinitError) {
        console.error('Failed to reinitialize database:', reinitError);
        return { success: false, error: `Database initialization failed: ${(reinitError as Error).message}` };
      }
    }

    // Test database connection with a simple query
    try {
      await databaseService.sqliteService.get('SELECT 1 as test');
      console.log('Database connection test successful');
    } catch (connectionError) {
      console.error('Database connection test failed:', connectionError);
      return { success: false, error: `Database connection failed: ${(connectionError as Error).message}` };
    }

    // Perform comprehensive database validation for critical operation
    console.log('Performing database validation for restaurant creation...');
    const validationResult = await databaseService.sqliteService.validateForCriticalOperation();

    if (!validationResult.valid) {
      console.error('Database validation failed:', validationResult.error);
      return { success: false, error: `Database validation failed: ${validationResult.error}` };
    }

    console.log('Database validation passed successfully');

    // First, validate that the user exists with retry logic for production
    let existingUser = null;
    let retryCount = 0;
    const maxRetries = 3;

    while (!existingUser && retryCount < maxRetries) {
      try {
        existingUser = await databaseService.sqliteService.get(`
          SELECT user_id, full_name FROM users WHERE user_id = ? AND is_active = 1
        `, [data.userId]);

        if (!existingUser && retryCount < maxRetries - 1) {
          console.log(`User validation attempt ${retryCount + 1} failed, retrying in 1 second...`);
          await new Promise(resolve => setTimeout(resolve, 1000));
          retryCount++;
        } else {
          break;
        }
      } catch (userCheckError) {
        console.error(`Error checking user existence (attempt ${retryCount + 1}):`, userCheckError);
        if (retryCount < maxRetries - 1) {
          await new Promise(resolve => setTimeout(resolve, 1000));
          retryCount++;
        } else {
          throw userCheckError;
        }
      }
    }

    console.log('User validation check:', existingUser ? `User exists: ${existingUser.full_name}` : 'User not found');

    if (!existingUser) {
      console.error(`User with ID ${data.userId} not found in database after ${maxRetries} attempts`);
      return { success: false, error: `User with ID ${data.userId} not found. Please complete user registration first.` };
    }

    // Check if restaurant already exists for this user
    const existingRestaurant = await databaseService.sqliteService.get(`
      SELECT * FROM restaurants WHERE user_id = ?
    `, [data.userId]);

    console.log('Existing restaurant check:', existingRestaurant ? 'Found' : 'Not found');

    if (existingRestaurant) {
      // Update existing restaurant
      console.log('Updating existing restaurant...');
      await databaseService.sqliteService.run(`
        UPDATE restaurants SET
          restaurant_name = ?, restaurant_address = ?, restaurant_type = ?,
          location = ?, machine_code = ?, phone = ?, email = ?, website = ?,
          description = ?, gst_number = ?, updated_at = ?
        WHERE user_id = ?
      `, [
        data.restaurantName, data.restaurantAddress, data.restaurantType,
        data.location, data.machineCode, data.phone || null, data.email || null,
        data.website || null, data.description || null, data.gstNumber || null,
        new Date().toISOString(), data.userId
      ]);
      console.log('Restaurant updated successfully');
    } else {
      // Create new restaurant
      console.log('Creating new restaurant...');
      console.log('Restaurant data to insert:', {
        userId: data.userId,
        restaurantName: data.restaurantName,
        restaurantAddress: data.restaurantAddress,
        restaurantType: data.restaurantType,
        location: data.location,
        machineCode: data.machineCode
      });

      try {
        // Use a transaction to ensure data consistency
        await databaseService.sqliteService.run('BEGIN TRANSACTION');

        try {
          await databaseService.sqliteService.run(`
            INSERT INTO restaurants (
              user_id, restaurant_name, restaurant_address, restaurant_type,
              location, machine_code, phone, email, website, description, gst_number,
              currency, currency_symbol, timezone, tax_rate, service_charge,
              is_active, created_at, updated_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
          `, [
            data.userId, data.restaurantName, data.restaurantAddress, data.restaurantType,
            data.location, data.machineCode, data.phone || null, data.email || null,
            data.website || null, data.description || null, data.gstNumber || null,
            'USD', '$', 'UTC', 0.0, 0.0, 1, new Date().toISOString(), new Date().toISOString()
          ]);

          // Commit the transaction
          await databaseService.sqliteService.run('COMMIT');
          console.log('Restaurant created successfully');
        } catch (transactionError) {
          // Rollback on error
          await databaseService.sqliteService.run('ROLLBACK');
          throw transactionError;
        }
      } catch (insertError) {
        console.error('Error inserting restaurant:', insertError);

        // Provide more specific error messages
        if (insertError instanceof Error && insertError.message && insertError.message.includes('FOREIGN KEY constraint failed')) {
          console.error('Foreign key constraint failed - user may not exist or foreign keys not enabled');
          return { success: false, error: 'User validation failed. Please restart the application and try again.' };
        }

        throw insertError; // Re-throw to be caught by outer try-catch
      }

      // Get the restaurant ID that was just created
      const restaurant = await databaseService.sqliteService.get(`
        SELECT id FROM restaurants WHERE user_id = ? ORDER BY created_at DESC LIMIT 1
      `, [data.userId]);

      console.log('Retrieved restaurant ID:', restaurant?.id);

      if (restaurant) {
        // Create sample menu items for new restaurant
        console.log('Creating sample menu items...');
        await databaseService.createSampleMenuItems(restaurant.id);
        console.log('Sample menu items created');
      }
    }

    // Mark setup as complete
    console.log('Marking setup as complete...');
    await databaseService.sqliteService.run(
      'INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)',
      ['setup_complete', '1']
    );

    await databaseService.sqliteService.run(
      'INSERT OR REPLACE INTO settings (key, value) VALUES (?, ?)',
      ['current_user_id', data.userId]
    );

    console.log('Restaurant data stored successfully');
    return { success: true };
  } catch (error) {
    console.error('Database error in store-restaurant-data:', error);
    console.error('Error details:', {
      message: (error as Error).message,
      stack: (error as Error).stack,
      data: data
    });
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('check-setup-status', async () => {
  try {
    // Check if setup is complete from settings
    const setupSetting = await databaseService.sqliteService.get(`
      SELECT value FROM settings WHERE key = 'setup_complete'
    `);

    // Count active users
    const userCount = await databaseService.sqliteService.get(`
      SELECT COUNT(*) as count FROM users WHERE is_active = 1
    `);

    const hasUsers = userCount?.count > 0;
    const isSetupComplete = setupSetting?.value === '1' && hasUsers;

    return {
      isSetupComplete,
      hasUsers
    };
  } catch (error) {
    console.error('Database error:', error);
    return { isSetupComplete: false, hasUsers: false };
  }
});

ipcMain.handle('get-restaurant-data', async () => {
  try {
    const restaurant = await databaseService.sqliteService.get(`
      SELECT user_id, restaurant_name, restaurant_address, restaurant_type,
             location, machine_code, phone, email, website, description,
             currency, currency_symbol, timezone, tax_rate, service_charge,
             created_at, updated_at
      FROM restaurants
      WHERE is_active = 1
      ORDER BY created_at DESC
      LIMIT 1
    `);

    if (!restaurant) return null;

    return {
      userId: restaurant.user_id,
      restaurantName: restaurant.restaurant_name,
      restaurantAddress: restaurant.restaurant_address,
      restaurantType: restaurant.restaurant_type,
      location: restaurant.location,
      machineCode: restaurant.machine_code,
      phone: restaurant.phone,
      email: restaurant.email,
      website: restaurant.website,
      description: restaurant.description,
      currency: restaurant.currency,
      currencySymbol: restaurant.currency_symbol,
      timezone: restaurant.timezone,
      taxRate: restaurant.tax_rate,
      serviceCharge: restaurant.service_charge,
      createdAt: restaurant.created_at
    };
  } catch (error) {
    console.error('Database error:', error);
    return null;
  }
});

ipcMain.handle('get-restaurant-details', async (_, restaurantId: string) => {
  try {
    // First try to find by user_id (string)
    let restaurant = await databaseService.sqliteService.get(`
      SELECT user_id, restaurant_name, restaurant_address, restaurant_type,
             location, machine_code, phone, email, website, description, gst_number,
             currency, currency_symbol, timezone, tax_rate, service_charge,
             created_at, updated_at
      FROM restaurants
      WHERE user_id = ? AND is_active = 1
      ORDER BY created_at DESC
      LIMIT 1
    `, [restaurantId]);

    // If not found and restaurantId looks like a number, try to find by database id
    if (!restaurant && /^\d+$/.test(restaurantId)) {
      restaurant = await databaseService.sqliteService.get(`
        SELECT user_id, restaurant_name, restaurant_address, restaurant_type,
               location, machine_code, phone, email, website, description, gst_number,
               currency, currency_symbol, timezone, tax_rate, service_charge,
               created_at, updated_at
        FROM restaurants
        WHERE id = ? AND is_active = 1
        ORDER BY created_at DESC
        LIMIT 1
      `, [parseInt(restaurantId)]);
    }

    if (!restaurant) return null;

    return {
      userId: restaurant.user_id,
      restaurantName: restaurant.restaurant_name,
      restaurantAddress: restaurant.restaurant_address,
      restaurantType: restaurant.restaurant_type,
      location: restaurant.location,
      machineCode: restaurant.machine_code,
      phone: restaurant.phone,
      email: restaurant.email,
      website: restaurant.website,
      description: restaurant.description,
      gstNumber: restaurant.gst_number,
      currency: restaurant.currency,
      currencySymbol: restaurant.currency_symbol,
      timezone: restaurant.timezone,
      taxRate: restaurant.tax_rate,
      serviceCharge: restaurant.service_charge,
      createdAt: restaurant.created_at
    };
  } catch (error) {
    console.error('Database error:', error);
    return null;
  }
});

// All other IPC handlers are set up in setupAllIpcHandlers() from ipcHandlers.ts

// Analytics runs silently in the background - no user-facing IPC handlers needed

// Backup Management IPC Handlers
ipcMain.handle('create-backup', async (_, filename?: string) => {
  try {
    const result = await backupService.createManualBackup(filename);
    return result;
  } catch (error) {
    console.error('Error creating backup:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('get-backups', async () => {
  try {
    const backups = await backupService.getBackups();
    return backups;
  } catch (error) {
    console.error('Error getting backups:', error);
    return [];
  }
});

ipcMain.handle('restore-backup', async (_, backupId: string) => {
  try {
    const result = await backupService.restoreBackup(backupId);
    return result;
  } catch (error) {
    console.error('Error restoring backup:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('delete-backup', async (_, backupId: string) => {
  try {
    const result = await backupService.deleteBackup(backupId);
    return result;
  } catch (error) {
    console.error('Error deleting backup:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('get-backup-stats', async () => {
  try {
    const stats = await backupService.getBackupStats();
    return stats;
  } catch (error) {
    console.error('Error getting backup stats:', error);
    return { totalBackups: 0, totalSize: 0 };
  }
});

ipcMain.handle('export-data', async (_, format: 'json' | 'csv', tables?: string[]) => {
  try {
    const result = await backupService.exportData(format, tables);
    return result;
  } catch (error) {
    console.error('Error exporting data:', error);
    return { success: false, error: (error as Error).message };
  }
});

// Database Health Check
ipcMain.handle('database-health-check', async () => {
  try {
    const health = await databaseService.healthCheck();
    return health;
  } catch (error) {
    console.error('Error checking database health:', error);
    return { healthy: false, details: { error: (error as Error).message } };
  }
});

// Database Diagnostics
ipcMain.handle('database-diagnostics', async () => {
  try {
    const diagnostics = await DatabaseDiagnostics.runDiagnostics();
    return diagnostics;
  } catch (error) {
    console.error('Error running database diagnostics:', error);
    return { success: false, details: { error: (error as Error).message } };
  }
});

// Auto-Updater IPC Handlers
ipcMain.handle('updater-check-for-updates', async () => {
  try {
    await autoUpdaterService.checkForUpdates();
    return { success: true };
  } catch (error) {
    console.error('Error checking for updates:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('updater-download-update', async () => {
  try {
    await autoUpdaterService.downloadUpdate();
    return { success: true };
  } catch (error) {
    console.error('Error downloading update:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('updater-quit-and-install', () => {
  try {
    autoUpdaterService.quitAndInstall();
    return { success: true };
  } catch (error) {
    console.error('Error installing update:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('updater-get-status', () => {
  try {
    const status = autoUpdaterService.getStatus();
    return { success: true, status };
  } catch (error) {
    console.error('Error getting updater status:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('updater-get-config', () => {
  try {
    const config = autoUpdaterService.getConfig();
    return { success: true, config };
  } catch (error) {
    console.error('Error getting updater config:', error);
    return { success: false, error: (error as Error).message };
  }
});

ipcMain.handle('updater-update-config', (_, newConfig) => {
  try {
    autoUpdaterService.updateConfig(newConfig);
    return { success: true };
  } catch (error) {
    console.error('Error updating updater config:', error);
    return { success: false, error: (error as Error).message };
  }
});

// Switch to dashboard mode after setup completion
ipcMain.handle('switch-to-fullscreen', async () => {
  try {
    if (mainWindow) {
      // Update window configuration for dashboard mode with default controls
      mainWindow.setFullScreen(false); // Don't force fullscreen
      mainWindow.maximize(); // Maximize the window instead
      mainWindow.setResizable(true);
      mainWindow.setMaximizable(true);
      mainWindow.setMinimizable(true);
      mainWindow.setMinimumSize(1024, 768);
      mainWindow.setTitle('Zyka POS - Restaurant Management System');

      console.log('Switched to dashboard mode with default window controls');
      return { success: true };
    }
    return { success: false, error: 'Main window not available' };
  } catch (error) {
    console.error('Error switching to dashboard mode:', error);
    return { success: false, error: (error as Error).message };
  }
});
