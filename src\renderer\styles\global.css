/* Global styles for Zyka POS */

* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* Icon Component Styles */
.icon {
  display: inline-block;
  vertical-align: middle;
  flex-shrink: 0;
}

.icon.w-3 {
  width: 0.75rem;
  height: 0.75rem;
}
.icon.w-4 {
  width: 1rem;
  height: 1rem;
}
.icon.w-5 {
  width: 1.25rem;
  height: 1.25rem;
}
.icon.w-6 {
  width: 1.5rem;
  height: 1.5rem;
}
.icon.w-8 {
  width: 2rem;
  height: 2rem;
}

.icon.inline-icon {
  margin-right: 0.5rem;
  color: #667eea;
}

/* Subscription Status Indicator */
.subscription-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 6px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
  border: 1px solid;
  transition: all 0.2s ease;
}

.subscription-status.trial {
  background: linear-gradient(135deg, #fef3c7 0%, #ffffff 100%);
  border-color: #fcd34d;
  color: #92400e;
}

.subscription-status.active {
  background: linear-gradient(135deg, #d1fae5 0%, #ffffff 100%);
  border-color: #a7f3d0;
  color: #065f46;
}

.subscription-status.expired {
  background: linear-gradient(135deg, #fef2f2 0%, #ffffff 100%);
  border-color: #fecaca;
  color: #991b1b;
}

.subscription-status .status-icon {
  font-size: 14px;
}

.subscription-status .status-info {
  display: flex;
  flex-direction: column;
  line-height: 1.2;
}

.subscription-status .status-label {
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.subscription-status .days-remaining {
  font-size: 10px;
  opacity: 0.8;
}

/* Sidebar Subscription Info */
.subscription-info {
  margin-bottom: 0;
}

.sidebar-footer .subscription-card {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px;
  padding: 20px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  margin-bottom: 0;
}

.sidebar-footer .subscription-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  border-color: rgba(255, 255, 255, 0.2);
}

.sidebar-footer .subscription-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
}

.sidebar-footer .subscription-card.trial::before {
  background: linear-gradient(90deg, transparent 0%, #fcd34d 50%, transparent 100%);
}

.sidebar-footer .subscription-card.active::before {
  background: linear-gradient(90deg, transparent 0%, #10b981 50%, transparent 100%);
}

.sidebar-footer .subscription-card.expired::before {
  background: linear-gradient(90deg, transparent 0%, #ef4444 50%, transparent 100%);
}

.sidebar-footer .subscription-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.sidebar-footer .subscription-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 12px;
  background: rgba(255, 255, 255, 0.15);
  transition: all 0.3s ease;
  flex-shrink: 0;
}

.sidebar-footer .subscription-plan-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
  flex: 1;
}

.sidebar-footer .subscription-card.trial .subscription-icon {
  background: rgba(252, 211, 77, 0.2);
  color: #fcd34d;
}

.sidebar-footer .subscription-card.active .subscription-icon {
  background: rgba(16, 185, 129, 0.2);
  color: #10b981;
}

.sidebar-footer .subscription-card.expired .subscription-icon {
  background: rgba(239, 68, 68, 0.2);
  color: #ef4444;
}

.sidebar-footer .subscription-plan {
  font-weight: 600;
  font-size: 14px;
  color: white;
  letter-spacing: 0.5px;
  margin: 0;
}

.sidebar-footer .subscription-status {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.7);
  font-weight: 400;
}

.sidebar-footer .subscription-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.sidebar-footer .days-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sidebar-footer .next-billing {
  font-size: 11px;
  color: rgba(255, 255, 255, 0.6);
}

.sidebar-footer .days-remaining {
  font-size: 12px;
  opacity: 0.7;
  color: rgba(255, 255, 255, 0.7);
}

.sidebar-footer .upgrade-btn,
.sidebar-footer .renew-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.1) 100%);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
  margin-top: 8px;
  justify-content: center;
}

.sidebar-footer .renew-btn {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.2) 0%, rgba(220, 38, 38, 0.15) 100%);
  border-color: rgba(239, 68, 68, 0.3);
}

.sidebar-footer .upgrade-btn:hover,
.sidebar-footer .renew-btn:hover {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.15) 100%);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.sidebar-footer .renew-btn:hover {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.25) 0%, rgba(220, 38, 38, 0.2) 100%);
}

body {
  font-family:
    -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell",
    sans-serif;
  background: #f8fafc;
  height: 100vh;
  overflow: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.app {
  height: 100vh;
  width: 100vw;
  display: flex;
  flex-direction: column;
}

/* Full-screen layout for POS */
.pos-layout {
  height: 100vh;
  width: 100vw;
  display: flex;
  background: #f8fafc;
}

/* Onboarding layout - centered for setup screens */
.onboarding-layout {
  height: 100vh;
  width: 100vw;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Onboarding Container - Optimized for 900x600 */
.onboarding-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 15px 40px rgba(0, 0, 0, 0.12);
  padding: 32px;
  width: 480px;
  max-width: 85vw;
  max-height: 85vh;
  text-align: center;
  animation: slideIn 0.5s ease-out;
  overflow-y: auto;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Typography - Compact for 900x600 */
.onboarding-title {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 6px;
}

.onboarding-subtitle {
  font-size: 14px;
  color: #718096;
  margin-bottom: 24px;
}

/* Form Styles - Compact */
.form-group {
  margin-bottom: 16px;
  text-align: left;
}

.form-label {
  display: block;
  font-size: 13px;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 6px;
}

.form-input {
  width: 100%;
  padding: 12px 14px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: #f7fafc;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-select {
  width: 100%;
  padding: 12px 14px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 14px;
  background: #f7fafc;
  cursor: pointer;
  transition: all 0.2s ease;
}

.form-select:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Button Styles - Touch Friendly & Compact */
.btn {
  padding: 14px 28px;
  border: none;
  border-radius: 10px;
  font-size: 15px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 48px;
  min-width: 120px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  text-decoration: none;
}

.btn-primary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.btn-primary:active {
  transform: translateY(0);
}

.btn-secondary {
  background: #e2e8f0;
  color: #4a5568;
}

.btn-secondary:hover {
  background: #cbd5e0;
  transform: translateY(-1px);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none !important;
}

/* Loading Spinner */
.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #ffffff40;
  border-top: 2px solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 8px;
}

/* Loading Container */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
}

.loading-container .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  margin-right: 0;
  margin-bottom: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Success/Error Messages - Compact */
.message {
  padding: 10px 14px;
  border-radius: 8px;
  margin-bottom: 16px;
  font-size: 13px;
  font-weight: 500;
}

.message-success {
  background: #c6f6d5;
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.message-error {
  background: #fed7d7;
  color: #742a2a;
  border: 1px solid #fc8181;
}

/* Step Indicator - Compact */
.step-indicator {
  display: flex;
  justify-content: center;
  margin-bottom: 24px;
}

.step-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #e2e8f0;
  margin: 0 6px;
  transition: all 0.2s ease;
}

.step-dot.active {
  background: #667eea;
  transform: scale(1.2);
}

.step-dot.completed {
  background: #48bb78;
}

/* Dashboard Styles - Full Screen POS Layout */
.dashboard {
  height: 100vh;
  width: 100vw;
  display: flex;
  background: #f8fafc;
  overflow: hidden;
}

/* Sidebar Navigation - Professional Design */
.dashboard-sidebar {
  width: 300px;
  background: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  color: white;
  display: flex;
  flex-direction: column;
  box-shadow: 8px 0 32px rgba(0, 0, 0, 0.15);
  z-index: 100;
  position: relative;
}

.dashboard-sidebar::before {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  width: 1px;
  height: 100%;
  background: linear-gradient(
    180deg,
    rgba(255, 255, 255, 0.1) 0%,
    rgba(255, 255, 255, 0.05) 50%,
    rgba(255, 255, 255, 0.1) 100%
  );
}

.dashboard-header {
  padding: 32px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.08);
  text-align: center;
  background: rgba(255, 255, 255, 0.02);
  position: relative;
}

.dashboard-header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 24px;
  right: 24px;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
}

.dashboard-title {
  font-size: 28px;
  font-weight: 800;
  color: white;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #ffffff 0%, #e2e8f0 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.dashboard-subtitle {
  font-size: 13px;
  color: rgba(255, 255, 255, 0.6);
  text-transform: uppercase;
  letter-spacing: 2px;
  font-weight: 600;
}

/* Main Content Area */
.dashboard-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.dashboard-topbar {
  height: 80px;
  background: white;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 32px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.dashboard-content {
  flex: 1;
  padding: 0;
  overflow-y: auto;
  background: #f8fafc;
}

.dashboard-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.dashboard-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  transition: all 0.3s ease;
}

.dashboard-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.card-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 6px;
}

.card-value {
  font-size: 20px;
  font-weight: 700;
  color: #667eea;
}

/* Modern Login Page Styles */
.login-container {
  background: white;
  border-radius: 24px;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  padding: 48px;
  width: 480px;
  max-width: 90vw;
  text-align: center;
  animation: slideInUp 0.6s ease-out;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-header {
  margin-bottom: 32px;
}

.login-logo {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.logo-icon {
  font-size: 48px;
  margin-right: 16px;
}

.logo-text {
  font-size: 32px;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-subtitle {
  font-size: 16px;
  color: #718096;
  font-weight: 500;
}

.login-form-container {
  margin-bottom: 32px;
}

.login-title {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 24px;
}

.login-message {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 20px;
  border-radius: 12px;
  margin-bottom: 24px;
  font-size: 14px;
  font-weight: 600;
}

.login-message-success {
  background: #c6f6d5;
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.login-message-error {
  background: #fed7d7;
  color: #742a2a;
  border: 1px solid #fc8181;
}

.message-icon {
  margin-right: 8px;
  font-size: 16px;
}

.login-form {
  text-align: center;
}

.pin-section {
  margin-bottom: 32px;
}

.pin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.pin-label {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #4a5568;
  margin: 0;
}

.input-toggle-btn {
  background: #f7fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.input-toggle-btn:hover {
  background: #edf2f7;
  border-color: #cbd5e0;
}

.input-toggle-btn:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.pin-input-container {
  position: relative;
  margin-bottom: 24px;
}

.pin-input-hidden {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 10;
  font-size: 16px;
  border: none;
  background: transparent;
  cursor: pointer;
}

.pin-input-hidden:focus {
  outline: none;
}

.pin-display {
  display: flex;
  justify-content: center;
  gap: 12px;
  cursor: pointer;
  user-select: none;
}

.pin-digit {
  width: 56px;
  height: 56px;
  border: 3px solid #e2e8f0;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  background: #f8fafc;
  transition: all 0.3s ease;
  cursor: pointer;
}

.pin-digit.filled {
  border-color: #667eea;
  background: #667eea;
  color: white;
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pin-digit.empty {
  border-color: #e2e8f0;
  background: #f8fafc;
}

.pin-digit:hover {
  border-color: #cbd5e0;
  background: #edf2f7;
}

.pin-input-container:focus-within .pin-digit.empty {
  border-color: #667eea;
  background: #f0f4ff;
}

/* Individual PIN Inputs */
.individual-pin-inputs {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 24px;
}

.individual-pin-input {
  width: 56px;
  height: 56px;
  border: 3px solid #e2e8f0;
  border-radius: 12px;
  text-align: center;
  font-size: 24px;
  font-weight: 700;
  background: #f8fafc;
  transition: all 0.3s ease;
  outline: none;
}

.individual-pin-input:focus {
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: scale(1.05);
}

.individual-pin-input:not(:placeholder-shown) {
  border-color: #667eea;
  background: #667eea;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.user-info {
  display: flex;
  align-items: center;
  background: #f8fafc;
  border-radius: 16px;
  padding: 20px;
  margin-bottom: 24px;
  text-align: left;
}

.user-avatar {
  width: 56px;
  height: 56px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
  margin-right: 16px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
}

.user-email {
  font-size: 14px;
  color: #718096;
}

.pin-help {
  background: #e6fffa;
  border: 1px solid #81e6d9;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 16px;
  color: #234e52;
  font-size: 14px;
}

.input-help {
  background: #f0f4ff;
  border: 1px solid #c3dafe;
  border-radius: 8px;
  padding: 12px;
  margin-bottom: 24px;
  color: #3c366b;
  font-size: 13px;
  text-align: center;
}

.input-help p {
  margin: 0;
}

.login-button {
  width: 100%;
  padding: 18px 24px;
  font-size: 16px;
  font-weight: 700;
  border-radius: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.4);
}

.login-button:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.login-footer {
  color: #718096;
  font-size: 14px;
  font-weight: 500;
}

/* Dashboard Card Details */
.card-details {
  text-align: left;
}

.card-details p {
  margin: 8px 0;
  font-size: 14px;
  color: #4a5568;
}

.card-subtitle {
  font-size: 14px;
  color: #718096;
  margin-top: 8px;
}

.status-active {
  color: #48bb78;
  font-weight: 600;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-top: 16px;
}

.action-btn {
  padding: 12px 16px;
  border: none;
  border-radius: 8px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.action-btn:active {
  transform: translateY(0);
}

/* Navigation Menu Styles - Professional Design */
.nav-menu {
  flex: 1;
  padding: 24px 0;
  overflow-y: auto;
  /* Hide scrollbar for webkit browsers */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* Internet Explorer 10+ */
}

.nav-menu::-webkit-scrollbar {
  display: none; /* Safari and Chrome */
}

.nav-item {
  display: flex;
  align-items: center;
  padding: 18px 24px;
  margin: 4px 16px;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 12px;
  cursor: pointer;
  position: relative;
  border: none;
  background: transparent;
  width: calc(100% - 32px);
  text-align: left;
}

.nav-item::before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, #3b82f6 0%, #1d4ed8 100%);
  border-radius: 0 4px 4px 0;
  transform: scaleY(0);
  transition: transform 0.3s ease;
}

.nav-item:hover {
  background: rgba(255, 255, 255, 0.08);
  color: rgba(255, 255, 255, 0.95);
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.nav-item:hover::before {
  transform: scaleY(1);
}

.nav-item.active {
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.15) 0%, rgba(29, 78, 216, 0.1) 100%);
  color: white;
  transform: translateX(4px);
  box-shadow: 0 4px 16px rgba(59, 130, 246, 0.2);
}

.nav-item.active::before {
  transform: scaleY(1);
}

.nav-icon {
  width: 24px;
  height: 24px;
  margin-right: 16px;
  font-size: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.3s ease;
}

.nav-item:hover .nav-icon {
  transform: scale(1.1);
}

.nav-text {
  font-size: 15px;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* POS Specific Components */
.pos-grid {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  height: 100%;
}

.menu-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
}

.order-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 20px;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

/* Menu Items Grid */
.menu-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
  gap: 16px;
  max-height: calc(100vh - 200px);
  overflow-y: auto;
}

.menu-item {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.menu-item:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.menu-item-name {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.menu-item-price {
  font-size: 18px;
  font-weight: 700;
  color: #667eea;
}

/* Order Display */
.order-items {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #e2e8f0;
}

.order-total {
  padding: 20px 0;
  border-top: 2px solid #e2e8f0;
  text-align: center;
}

.total-amount {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-online {
  background: #c6f6d5;
  color: #22543d;
}

.status-offline {
  background: #fed7d7;
  color: #742a2a;
}

/* Responsive Design for Full Screen */
@media (max-width: 1200px) {
  .dashboard-sidebar {
    width: 240px;
  }

  .pos-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .menu-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  }
}

@media (max-width: 768px) {
  .dashboard-sidebar {
    width: 200px;
  }

  .dashboard-content {
    padding: 16px;
  }

  .menu-grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 12px;
  }
}

/* Additional Dashboard Styles */
.topbar-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.topbar-title-section {
  display: flex;
  align-items: center;
  gap: 16px;
}

.dashboard-main-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  line-height: 1.2;
}

.current-view-title {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.restaurant-name {
  font-size: 14px;
  color: #718096;
  background: #f7fafc;
  padding: 4px 12px;
  border-radius: 20px;
}

.topbar-right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.current-time {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

.new-order-btn {
  background: #3182ce;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.new-order-btn:hover {
  background: #2c5aa0;
  transform: translateY(-1px);
}

.logout-btn {
  background: #e53e3e;
  color: white;
  border: none;
  padding: 10px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 40px;
  height: 40px;
}

.logout-btn:hover {
  background: #c53030;
  transform: translateY(-1px);
}

/* Mobile PIN Login Styles */
.mobile-pin-login {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.pin-login-container {
  background: white;
  border-radius: 20px;
  padding: 40px 30px;
  width: 100%;
  max-width: 400px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  text-align: center;
}

.pin-login-header {
  margin-bottom: 30px;
}

.restaurant-info .restaurant-name {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0 0 8px 0;
}

.restaurant-info .login-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

.pin-message {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 12px;
  margin-bottom: 20px;
  font-size: 14px;
  font-weight: 500;
}

.pin-message-success {
  background: #c6f6d5;
  color: #22543d;
  border: 1px solid #9ae6b4;
}

.pin-message-error {
  background: #fed7d7;
  color: #742a2a;
  border: 1px solid #fc8181;
}

.user-info-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 30px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 16px;
}

.user-avatar {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  font-weight: 700;
}

.user-details {
  text-align: left;
}

.user-id {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 4px 0;
}

.user-email {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.pin-display-section {
  margin-bottom: 30px;
}

.pin-title {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 20px 0;
}

.pin-dots {
  display: flex;
  justify-content: center;
  gap: 12px;
  margin-bottom: 20px;
}

.pin-dot {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  border: 2px solid #e2e8f0;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  transition: all 0.2s ease;
}

.pin-dot.filled {
  background: #667eea;
  border-color: #667eea;
  color: white;
}

.keyboard-help {
  margin-top: 16px;
  text-align: center;
}

.keyboard-help p {
  margin: 0 0 8px 0;
  font-size: 14px;
  color: #718096;
}

.keyboard-shortcuts {
  font-size: 12px !important;
  color: #a0aec0 !important;
}

.keyboard-shortcuts span {
  font-weight: 500;
  color: #4a5568;
}

.mobile-keypad {
  margin-bottom: 30px;
}

.keypad-row {
  display: flex;
  justify-content: center;
  gap: 15px;
  margin-bottom: 15px;
}

.keypad-btn {
  width: 70px;
  height: 70px;
  border-radius: 50%;
  border: none;
  background: #f8fafc;
  color: #2d3748;
  font-size: 24px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.keypad-btn:hover {
  background: #e2e8f0;
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.keypad-btn:active {
  transform: translateY(0);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.keypad-btn.keypad-action {
  background: #667eea;
  color: white;
  font-size: 16px;
  font-weight: 500;
}

.keypad-btn.keypad-action:hover {
  background: #5a67d8;
}

.pin-actions {
  margin-bottom: 20px;
}

.pin-submit-btn {
  width: 100%;
  padding: 16px;
  font-size: 18px;
  font-weight: 600;
  border-radius: 12px;
}

.pin-help {
  background: #e6fffa;
  border: 1px solid #81e6d9;
  border-radius: 12px;
  padding: 12px;
  margin-bottom: 20px;
  color: #234e52;
  font-size: 14px;
}

.pin-login-footer {
  color: #718096;
  font-size: 12px;
  font-weight: 500;
}

.pin-login-footer p {
  margin: 0;
}

.user-profile {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* Dashboard Controls Section */
.dashboard-controls {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 16px 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
}

.controls-left {
  flex: 1;
}

.overview-title {
  font-size: 16px;
  font-weight: 400;
  color: #718096;
  margin: 0;
}

.controls-center {
  display: flex;
  align-items: center;
  justify-content: center;
  flex: 1;
}

.date-selector {
  display: flex;
  align-items: center;
  gap: 12px;
  background: #f8fafc;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.date-dropdown {
  background: transparent;
  border: none;
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  cursor: pointer;
  outline: none;
}

.current-date {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.controls-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
}

.control-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: #f8fafc;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  color: #718096;
}

.icon-btn:hover {
  background: #e2e8f0;
  color: #2d3748;
}

.refresh-data-btn {
  background: #38b2ac;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-data-btn:hover {
  background: #319795;
  transform: translateY(-1px);
}

/* Tax Management Styles */
.tax-management {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: #f8fafc;
  min-height: 100vh;
}

.tax-management-header {
  margin-bottom: 32px;
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
}

.tax-management-header .header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 32px;
}

.tax-management-header .header-info {
  flex: 1;
}

.tax-management-header .section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8px;
}

.tax-management-header .section-description {
  color: #718096;
  font-size: 16px;
  margin-bottom: 24px;
  line-height: 1.6;
}

.tax-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 16px;
  background: #f7fafc;
  border-radius: 12px;
  min-width: 80px;
}

.stat-number {
  font-size: 24px;
  font-weight: 700;
  color: #667eea;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  font-weight: 600;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: flex-start;
}

/* Search and Filter Controls */
.tax-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 24px;
  margin-bottom: 32px;
  background: white;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
}

.search-section {
  flex: 1;
  max-width: 400px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-input-wrapper svg {
  position: absolute;
  left: 16px;
  color: #718096;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 48px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: #f8fafc;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.clear-search {
  position: absolute;
  right: 12px;
  background: none;
  border: none;
  color: #718096;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: #e2e8f0;
  color: #2d3748;
}

.filter-section {
  display: flex;
  gap: 12px;
}

.filter-select {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 14px;
  background: #f8fafc;
  color: #2d3748;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 140px;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

/* Toast Notifications */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  min-width: 320px;
  max-width: 500px;
  border-radius: 12px;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease-out;
  backdrop-filter: blur(8px);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
}

.toast-icon {
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
  line-height: 1.4;
}

.toast-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.toast-close:hover {
  opacity: 1;
  background: rgba(0, 0, 0, 0.1);
}

.toast-success {
  background: linear-gradient(135deg, #f0fff4 0%, #c6f6d5 100%);
  border: 1px solid #9ae6b4;
  color: #22543d;
}

.toast-error {
  background: linear-gradient(135deg, #fed7d7 0%, #feb2b2 100%);
  border: 1px solid #feb2b2;
  color: #742a2a;
}

.toast-info {
  background: linear-gradient(135deg, #ebf8ff 0%, #bee3f8 100%);
  border: 1px solid #90cdf4;
  color: #2a4365;
}

.toast-warning {
  background: linear-gradient(135deg, #fefcbf 0%, #faf089 100%);
  border: 1px solid #f6e05e;
  color: #744210;
}

.toast-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 12px;
}

.toast-icon {
  font-size: 18px;
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.toast-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: inherit;
  opacity: 0.7;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.toast-close:hover {
  opacity: 1;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Common Taxes Section */
.common-taxes {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 32px;
}

.common-taxes h4 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.common-taxes-description {
  color: #718096;
  font-size: 14px;
  margin-bottom: 20px;
}

.common-tax-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.common-tax-btn {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.common-tax-btn:hover:not(:disabled) {
  border-color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.common-tax-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.tax-btn-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.tax-btn-name {
  font-weight: 500;
  color: #2d3748;
  font-size: 14px;
}

.tax-btn-rate {
  font-weight: 600;
  color: #667eea;
  font-size: 14px;
}

/* Professional Status Icons */
.status-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.status-item.available .status-icon {
  background: #48bb78;
}

.status-item.occupied .status-icon {
  background: #e53e3e;
}

.status-item.reserved .status-icon {
  background: #ed8936;
}

.status-item.maintenance .status-icon {
  background: #718096;
}

/* Tax Form Modal */
.tax-form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.tax-form-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(4px);
}

.tax-form-container {
  position: relative;
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.tax-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px 24px 0;
  margin-bottom: 24px;
}

.tax-form-header h3 {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #718096;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #f7fafc;
  color: #2d3748;
}

.tax-form {
  padding: 0 24px 24px;
}

.form-group {
  margin-bottom: 20px;
}

.form-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #2d3748;
  margin-bottom: 6px;
}

.form-input,
.form-select {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s ease;
  background: white;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error,
.form-select.error {
  border-color: #e53e3e;
  box-shadow: 0 0 0 3px rgba(229, 62, 62, 0.1);
}

.error-message {
  display: block;
  color: #e53e3e;
  font-size: 12px;
  margin-top: 4px;
  font-weight: 500;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.checkbox-label {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  cursor: pointer;
  padding: 12px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.checkbox-label:hover {
  background: #f8fafc;
}

.checkbox-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.checkbox-label input[type="checkbox"] {
  margin: 0;
  width: 18px;
  height: 18px;
  accent-color: #667eea;
  cursor: pointer;
  position: relative;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: 2px solid #d1d5db;
  border-radius: 4px;
  background: white;
  transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked {
  background: #667eea;
  border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:checked::after {
  content: "✓";
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
}

.checkbox-label input[type="checkbox"]:hover {
  border-color: #667eea;
}

.checkbox-label input[type="checkbox"]:focus {
  outline: 2px solid #667eea;
  outline-offset: 2px;
}

.checkbox-text {
  font-weight: 500;
  color: #2d3748;
  font-size: 14px;
}

.checkbox-description {
  display: block;
  color: #718096;
  font-size: 12px;
  margin-top: 2px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 32px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

/* Tax Rates Section */
.tax-rates-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
}

.section-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
}

.section-header h4 {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 4px;
}

.search-results {
  color: #718096;
  font-size: 14px;
  margin: 0;
}

.tax-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  gap: 24px;
}

.tax-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  padding: 24px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.tax-card::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.tax-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  border-color: #cbd5e0;
}

.tax-card:hover::before {
  opacity: 1;
}

.tax-card.default {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.tax-card.default::before {
  background: linear-gradient(90deg, #f59e0b 0%, #d97706 100%);
  opacity: 1;
}

.tax-card.inactive {
  opacity: 0.7;
  background: #f8fafc;
  border-color: #cbd5e0;
}

.tax-card.inactive::before {
  background: #9ca3af;
}

.tax-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 20px;
}

.tax-info {
  flex: 1;
}

.tax-name {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  line-height: 1.3;
  margin-bottom: 8px;
}

.tax-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: #718096;
}

.tax-id,
.tax-created {
  font-weight: 500;
}

.tax-badges {
  display: flex;
  flex-direction: column;
  gap: 6px;
  align-items: flex-end;
}

.badge {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  white-space: nowrap;
}

.default-badge {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  color: white;
  box-shadow: 0 2px 8px rgba(245, 158, 11, 0.3);
}

.inactive-badge {
  background: #f1f5f9;
  color: #64748b;
  border: 1px solid #cbd5e0;
}

.type-badge {
  background: #e0e7ff;
  color: #5b21b6;
  border: 1px solid #c4b5fd;
}

.type-badge.percentage {
  background: #ecfdf5;
  color: #065f46;
  border: 1px solid #a7f3d0;
}

.type-badge.fixed {
  background: #fef3c7;
  color: #92400e;
  border: 1px solid #fcd34d;
}

.tax-card-body {
  margin-bottom: 24px;
  text-align: center;
}

.tax-rate-display {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.tax-rate {
  font-size: 32px;
  font-weight: 800;
  color: #667eea;
  margin-bottom: 8px;
  line-height: 1;
}

.tax-card.default .tax-rate {
  color: #d97706;
}

.tax-card.inactive .tax-rate {
  color: #9ca3af;
}

.tax-type {
  font-size: 14px;
  color: #718096;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tax-card-actions {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  padding: 10px 12px;
  border: 2px solid transparent;
  border-radius: 10px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-btn.active {
  background: #d1fae5;
  color: #065f46;
  border-color: #a7f3d0;
}

.status-btn.inactive {
  background: #fee2e2;
  color: #991b1b;
  border-color: #fca5a5;
}

.default-btn {
  background: #fef3c7;
  color: #92400e;
  border-color: #fcd34d;
}

.edit-btn {
  background: #dbeafe;
  color: #1e40af;
  border-color: #93c5fd;
}

.delete-btn {
  background: #fee2e2;
  color: #991b1b;
  border-color: #fca5a5;
}

.action-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: currentColor;
}

.action-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
  background: #f1f5f9;
  color: #9ca3af;
  border-color: #d1d5db;
}

/* Empty State */
.empty-taxes {
  text-align: center;
  padding: 80px 40px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px dashed #cbd5e0;
  border-radius: 20px;
  margin: 40px 0;
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 24px;
  display: block;
  color: #9ca3af;
}

.empty-taxes h3 {
  font-size: 24px;
  font-weight: 700;
  color: #374151;
  margin-bottom: 12px;
}

.empty-taxes p {
  color: #6b7280;
  font-size: 16px;
  margin-bottom: 32px;
  line-height: 1.6;
}

.empty-actions {
  display: flex;
  justify-content: center;
  gap: 12px;
}

/* Loading State */
.settings-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.settings-loading p {
  color: #718096;
  font-size: 16px;
  font-weight: 500;
}

.activity-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #667eea;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.activity-item.warning .activity-icon {
  background: #ed8936;
}

.activity-item.info .activity-icon {
  background: #3182ce;
}

.user-name {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.card-content {
  flex: 1;
}

.metric-value {
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.metric-change {
  font-size: 14px;
  color: #718096;
}

.metric-description {
  font-size: 12px;
  color: #a0aec0;
  margin-top: 4px;
  font-style: italic;
}

.metric-change.positive {
  color: #48bb78;
}

.metric-change.negative {
  color: #f56565;
}

.dashboard-card.large {
  grid-column: span 2;
}

.quick-actions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.quick-action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.quick-action-btn:hover {
  border-color: #667eea;
  background: #edf2f7;
  transform: translateY(-2px);
}

.action-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.action-text {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e2e8f0;
}

.info-item:last-child {
  border-bottom: none;
}

.info-label {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.info-value {
  font-size: 14px;
  color: #2d3748;
  font-weight: 600;
}

.sidebar-footer {
  padding: 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.08);
  margin-top: auto;
  background: rgba(0, 0, 0, 0.1);
  position: relative;
}

.sidebar-footer::before {
  content: "";
  position: absolute;
  top: 0;
  left: 24px;
  right: 24px;
  height: 1px;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.15) 50%,
    transparent 100%
  );
}

.system-info {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 10px;
  padding: 16px;
  border: 1px solid rgba(255, 255, 255, 0.05);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 12px;
}

.info-label {
  color: rgba(255, 255, 255, 0.6);
  font-weight: 500;
}

.info-value {
  color: rgba(255, 255, 255, 0.9);
  font-weight: 600;
  font-family: "Courier New", monospace;
  background: rgba(255, 255, 255, 0.05);
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 11px;
}

/* Legacy support for old info-line class */
.info-line {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.5);
  margin-bottom: 6px;
  font-weight: 500;
  letter-spacing: 0.5px;
}

.info-line:last-child {
  margin-bottom: 0;
}

.empty-order {
  text-align: center;
  padding: 40px 20px;
  color: #718096;
}

.empty-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
}

.empty-subtitle {
  font-size: 14px;
  margin-top: 8px;
}

.checkout-btn {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 16px;
}

.checkout-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.checkout-btn:not(:disabled):hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.add-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 2px dashed #cbd5e0;
  background: transparent;
  color: #718096;
}

.add-item:hover {
  border-color: #667eea;
  color: #667eea;
  background: rgba(102, 126, 234, 0.05);
}

.add-icon {
  font-size: 32px;
  margin-bottom: 8px;
}

.add-text {
  font-size: 14px;
  font-weight: 600;
}

.coming-soon {
  text-align: center;
  padding: 80px 20px;
  color: #718096;
}

.coming-soon h3 {
  font-size: 24px;
  color: #2d3748;
  margin-bottom: 12px;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #48bb78;
  margin-right: 6px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
  100% {
    opacity: 1;
  }
}

/* Menu Item Card Styles */
.menu-item-image {
  width: 100%;
  height: 120px;
  overflow: hidden;
  border-radius: 8px;
  margin-bottom: 12px;
}

.menu-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.menu-item-content {
  position: relative;
}

.menu-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.menu-item-description {
  font-size: 12px;
  color: #718096;
  margin-bottom: 8px;
  line-height: 1.4;
}

.menu-item-category {
  font-size: 11px;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 2px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.menu-item.unavailable {
  opacity: 0.6;
  cursor: not-allowed;
  position: relative;
}

.unavailable-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
}

/* Order Display Styles */
.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.clear-order-btn {
  background: #fed7d7;
  color: #742a2a;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-order-btn:hover {
  background: #fc8181;
  color: white;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 0;
  border-bottom: 1px solid #e2e8f0;
}

.order-item:last-child {
  border-bottom: none;
}

.order-item-details {
  flex: 1;
}

.order-item-name {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
}

.order-item-price {
  font-size: 12px;
  color: #718096;
}

.order-item-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-btn {
  width: 28px;
  height: 28px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s ease;
}

.quantity-btn:hover:not(:disabled) {
  border-color: #667eea;
  background: #f7fafc;
}

.quantity-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quantity-display {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  min-width: 20px;
  text-align: center;
}

.order-item-subtotal {
  font-size: 14px;
  font-weight: 700;
  color: #2d3748;
  min-width: 60px;
  text-align: right;
}

.remove-item-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: #fed7d7;
  color: #742a2a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.remove-item-btn:hover {
  background: #fc8181;
  color: white;
}

.order-summary {
  padding: 20px 0;
  border-top: 2px solid #e2e8f0;
  margin-top: 20px;
}

.summary-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.summary-line.total-line {
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
  padding-top: 8px;
  border-top: 1px solid #e2e8f0;
  margin-top: 8px;
}

.order-actions {
  margin-top: 20px;
}

/* Payment Modal Styles */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.payment-modal {
  background: white;
  border-radius: 16px;
  width: 500px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  animation: slideInScale 0.3s ease;
}

@keyframes slideInScale {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f7fafc;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  color: #718096;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background: #e2e8f0;
  color: #2d3748;
}

.modal-content {
  padding: 24px;
}

.payment-summary {
  text-align: center;
  margin-bottom: 32px;
}

.total-amount {
  font-size: 32px;
  font-weight: 700;
  color: #2d3748;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.payment-methods h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
}

.method-buttons {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-bottom: 24px;
}

.method-btn {
  padding: 16px 12px;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 600;
  color: #4a5568;
  text-align: center;
}

.method-btn:hover {
  border-color: #667eea;
  background: #f7fafc;
}

.method-btn.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.cash-payment,
.card-payment,
.digital-payment {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.amount-input {
  width: 100%;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  text-align: center;
  transition: all 0.2s ease;
}

.amount-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.change-display {
  margin-top: 16px;
  text-align: center;
}

.change-amount {
  font-size: 20px;
  font-weight: 700;
  color: #48bb78;
  background: #c6f6d5;
  padding: 12px 20px;
  border-radius: 8px;
  display: inline-block;
}

.card-instructions,
.digital-instructions {
  text-align: center;
  color: #4a5568;
}

.card-instructions p,
.digital-instructions p {
  margin-bottom: 8px;
  font-size: 14px;
}

.modal-actions {
  display: flex;
  gap: 12px;
  padding: 24px;
  border-top: 1px solid #e2e8f0;
}

.cancel-btn {
  flex: 1;
  padding: 16px;
  border: 2px solid #e2e8f0;
  background: white;
  color: #4a5568;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.cancel-btn:hover {
  border-color: #cbd5e0;
  background: #f7fafc;
}

.process-btn {
  flex: 2;
  padding: 16px;
  border: none;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
}

.process-btn:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

.process-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Menu Management Styles */
.menu-management {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.menu-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

.menu-management-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.menu-management-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #718096;
}

.menu-management-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  margin-bottom: 20px;
}

/* Menu Form Modal */
.menu-form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-form-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.menu-form-container {
  background: white;
  border-radius: 16px;
  width: 600px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  position: relative;
  z-index: 1001;
}

.menu-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.menu-form-header h3 {
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.menu-form {
  padding: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 16px;
}

.form-actions {
  display: flex;
  gap: 12px;
  justify-content: flex-end;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

/* Menu Controls */
.menu-controls {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.menu-controls .search-section {
  margin-bottom: 20px;
}

.menu-controls .search-input-container {
  position: relative;
  max-width: 400px;
}

.menu-controls .search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.menu-controls .search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.menu-controls .search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #718096;
}

.menu-controls .filter-section {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
  margin-bottom: 20px;
}

.menu-controls .filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.menu-controls .filter-label {
  font-size: 12px;
  font-weight: 600;
  color: #4a5568;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.menu-controls .filter-select {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  min-width: 120px;
}

.menu-controls .filter-select:focus {
  outline: none;
  border-color: #667eea;
}

.menu-controls .view-toggle {
  display: flex;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
}

.menu-controls .view-btn {
  padding: 8px 12px;
  border: none;
  background: white;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.menu-controls .view-btn:hover {
  background: #f7fafc;
}

.menu-controls .view-btn.active {
  background: #667eea;
  color: white;
}

.menu-controls .menu-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
  font-size: 14px;
  color: #718096;
}

.menu-controls .results-count {
  font-weight: 500;
}

.menu-controls .pagination {
  display: flex;
  align-items: center;
  gap: 12px;
}

.menu-controls .page-btn {
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 4px;
}

.menu-controls .page-btn:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.menu-controls .page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.menu-controls .page-info {
  font-weight: 500;
  color: #4a5568;
}

/* Menu Items Grid */
.menu-items-grid {
  display: grid;
  gap: 24px;
}

.menu-items-grid.grid {
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
}

.menu-items-grid.list {
  grid-template-columns: 1fr;
  gap: 12px;
}

.empty-menu {
  grid-column: 1 / -1;
  text-align: center;
  padding: 80px 20px;
  color: #718096;
}

.empty-menu .empty-icon {
  font-size: 64px;
  display: block;
  margin-bottom: 16px;
}

.empty-menu h3 {
  font-size: 24px;
  color: #2d3748;
  margin-bottom: 8px;
}

.empty-menu p {
  margin-bottom: 24px;
}

/* Menu Item Card */
.menu-item-card {
  background: white;
  border-radius: 16px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.menu-item-card.list {
  display: flex;
  align-items: center;
  padding: 16px;
  gap: 20px;
  border-radius: 12px;
}

.menu-item-card.list .menu-item-image {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  border-radius: 8px;
  overflow: hidden;
}

.menu-item-card.list .menu-item-content {
  flex: 1;
  padding: 0;
}

.menu-item-card.list .menu-item-header {
  margin-bottom: 8px;
}

.menu-item-card.list .menu-item-description {
  margin-bottom: 12px;
}

.menu-item-card.list .menu-item-actions {
  margin-left: auto;
  padding: 0;
  border-top: none;
}

.menu-item-card.list .menu-item-actions .action-buttons {
  display: flex;
  gap: 8px;
}

/* Responsive Design for Menu Management */
@media (max-width: 768px) {
  .menu-controls {
    padding: 16px;
  }

  .menu-controls .filter-section {
    flex-direction: column;
    gap: 12px;
  }

  .menu-controls .filter-group {
    width: 100%;
  }

  .menu-controls .filter-select {
    width: 100%;
  }

  .menu-controls .menu-info {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .menu-controls .pagination {
    justify-content: center;
  }

  .menu-items-grid.grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .menu-item-card.list {
    flex-direction: column;
    gap: 12px;
  }

  .menu-item-card.list .menu-item-image {
    width: 100%;
    height: 200px;
  }

  .menu-item-card.list .menu-item-actions {
    margin-left: 0;
    width: 100%;
  }

  .menu-item-card.list .menu-item-actions .action-buttons {
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .menu-items-grid.grid {
    grid-template-columns: 1fr;
  }

  .menu-controls .search-input-container {
    max-width: 100%;
  }

  .menu-item-actions .action-buttons {
    flex-wrap: wrap;
    gap: 6px;
  }

  .menu-item-actions .action-buttons button {
    font-size: 12px;
    padding: 6px 8px;
  }
}

.menu-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.menu-item-card.unavailable {
  opacity: 0.7;
  background: #f7fafc;
}

.menu-item-card .menu-item-image {
  width: 100%;
  height: 160px;
  overflow: hidden;
  border-radius: 12px;
  margin-bottom: 16px;
}

.menu-item-card .menu-item-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.menu-item-card .menu-item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
}

.menu-item-card .menu-item-name {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
  flex: 1;
}

.menu-item-card .menu-item-price {
  font-size: 18px;
  font-weight: 700;
  color: #667eea;
  margin-left: 12px;
}

.menu-item-card .menu-item-description {
  font-size: 14px;
  color: #718096;
  margin-bottom: 12px;
  line-height: 1.4;
}

.menu-item-card .menu-item-category {
  font-size: 12px;
  color: #667eea;
  background: rgba(102, 126, 234, 0.1);
  padding: 4px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  font-weight: 600;
  letter-spacing: 0.5px;
  display: inline-block;
  margin-bottom: 16px;
}

.menu-item-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.menu-item-actions button {
  padding: 8px 12px;
  border: none;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.availability-btn.available {
  background: #c6f6d5;
  color: #22543d;
}

.availability-btn.unavailable {
  background: #fed7d7;
  color: #742a2a;
}

.edit-btn {
  background: #bee3f8;
  color: #2a69ac;
}

.delete-btn {
  background: #fed7d7;
  color: #742a2a;
}

.menu-item-actions button:hover {
  transform: translateY(-1px);
  opacity: 0.9;
}

/* CSV Import Modal Styles */
.csv-import-modal {
  background: white;
  border-radius: 16px;
  width: 800px;
  max-width: 95vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
}

.import-instructions {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  margin-bottom: 24px;
}

.import-instructions h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
}

.import-instructions ul {
  margin: 12px 0;
  padding-left: 20px;
}

.import-instructions li {
  margin-bottom: 4px;
  font-size: 14px;
  color: #4a5568;
}

.import-methods {
  margin-bottom: 24px;
}

.method-section {
  margin-bottom: 20px;
}

.method-section h4 {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.file-input {
  width: 100%;
  padding: 12px;
  border: 2px dashed #cbd5e0;
  border-radius: 8px;
  background: #f7fafc;
  cursor: pointer;
  transition: all 0.2s ease;
}

.file-input:hover {
  border-color: #667eea;
  background: #edf2f7;
}

.method-divider {
  text-align: center;
  margin: 20px 0;
  font-weight: 600;
  color: #718096;
  position: relative;
}

.method-divider::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 1px;
  background: #e2e8f0;
  z-index: 1;
}

.method-divider span {
  background: white;
  padding: 0 16px;
  position: relative;
  z-index: 2;
}

.csv-textarea {
  width: 100%;
  padding: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-family: "Courier New", monospace;
  font-size: 12px;
  resize: vertical;
  min-height: 120px;
}

.csv-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.preview-section {
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
}

.preview-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
}

.preview-stats {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.stat {
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
}

.stat.valid {
  background: #c6f6d5;
  color: #22543d;
}

.stat.invalid {
  background: #fed7d7;
  color: #742a2a;
}

.preview-table {
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.preview-table table {
  width: 100%;
  border-collapse: collapse;
}

.preview-table th {
  background: #f7fafc;
  padding: 12px;
  text-align: left;
  font-weight: 600;
  color: #2d3748;
  border-bottom: 1px solid #e2e8f0;
}

.preview-table td {
  padding: 12px;
  border-bottom: 1px solid #e2e8f0;
  font-size: 14px;
  color: #4a5568;
}

.preview-table tr.error-row {
  background: #fef5e7;
}

.status-valid {
  color: #22543d;
  font-weight: 600;
}

.status-invalid {
  color: #742a2a;
  font-weight: 600;
  cursor: help;
}

.preview-note {
  padding: 12px;
  text-align: center;
  color: #718096;
  font-style: italic;
  background: #f7fafc;
  margin: 0;
}

/* Image Upload Styles */
.image-upload {
  margin-bottom: 20px;
}

.image-upload-container {
  border: 2px dashed #cbd5e0;
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 16px;
  transition: all 0.3s ease;
}

.image-upload-container:hover {
  border-color: #667eea;
}

.image-preview {
  position: relative;
  width: 100%;
  height: 200px;
}

.preview-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.change-image-btn,
.remove-image-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.change-image-btn {
  background: #667eea;
  color: white;
}

.change-image-btn:hover:not(:disabled) {
  background: #5a67d8;
}

.remove-image-btn {
  background: #e53e3e;
  color: white;
}

.remove-image-btn:hover:not(:disabled) {
  background: #c53030;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
  color: #718096;
}

.upload-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.7;
}

.upload-placeholder p {
  margin-bottom: 16px;
  font-size: 16px;
}

.upload-btn {
  padding: 12px 24px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-btn:hover:not(:disabled) {
  background: #5a67d8;
  transform: translateY(-1px);
}

.upload-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.url-input-section {
  margin-bottom: 16px;
}

.upload-help {
  background: #f8fafc;
  border-radius: 8px;
  padding: 12px;
  border: 1px solid #e2e8f0;
}

.upload-help p {
  margin: 4px 0;
  font-size: 12px;
  color: #718096;
}

/* Image Resolver Styles */
.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f7fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #718096;
}

.image-placeholder.loading {
  padding: 20px;
}

.image-placeholder.no-image {
  padding: 16px;
}

.placeholder-icon {
  font-size: 24px;
  margin-bottom: 4px;
  opacity: 0.7;
}

.placeholder-text {
  font-size: 12px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

/* POS View Styles */
.pos-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #718096;
}

.pos-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  margin-bottom: 20px;
}

.empty-menu-pos {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: #718096;
  background: #f8fafc;
  border-radius: 16px;
  border: 2px dashed #cbd5e0;
}

.empty-menu-pos .empty-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
}

.empty-menu-pos h4 {
  font-size: 20px;
  color: #2d3748;
  margin-bottom: 8px;
}

.empty-menu-pos p {
  margin-bottom: 0;
}

.menu-item-pos {
  background: white;
  border-radius: 16px;
  padding: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
}

.menu-item-pos:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.menu-item-pos .menu-item-image {
  width: 100%;
  height: 140px;
  overflow: hidden;
  border-radius: 12px;
  margin-bottom: 12px;
}

.menu-item-pos .menu-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.add-to-order-btn {
  width: 100%;
  padding: 12px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  margin-top: 12px;
}

.add-to-order-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

/* Settings Page Styles */
.settings-page {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  min-height: 100vh;
  background: #f8fafc;
}

.settings-header {
  margin-bottom: 32px;
  text-align: center;
}

.settings-title {
  font-size: 32px;
  font-weight: 800;
  color: #1a202c;
  margin-bottom: 8px;
}

.settings-subtitle {
  font-size: 16px;
  color: #718096;
  margin: 0;
}

.settings-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  overflow: hidden;
}

.settings-tabs-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-bottom: 1px solid #e2e8f0;
  padding: 24px;
  border-radius: 16px 16px 0 0;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.settings-tabs-nav {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  max-width: 100%;
}

@media (max-width: 1200px) {
  .settings-tabs-nav {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 768px) {
  .settings-tabs-nav {
    grid-template-columns: repeat(2, 1fr);
  }
}

.settings-tab-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  min-height: 80px;
  text-align: center;
}

.settings-tab-item:hover {
  background: rgba(255, 255, 255, 0.2);
  border-color: rgba(255, 255, 255, 0.4);
  transform: translateY(-2px);
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.settings-tab-item.active {
  background: rgba(255, 255, 255, 0.25);
  border-color: rgba(255, 255, 255, 0.6);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  transform: translateY(-2px);
}

.tab-item-icon {
  font-size: 24px;
  margin-bottom: 8px;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.3s ease;
}

.settings-tab-item:hover .tab-item-icon,
.settings-tab-item.active .tab-item-icon {
  color: white;
  transform: scale(1.1);
}

.tab-item-label {
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  line-height: 1.2;
  transition: all 0.3s ease;
}

.settings-tab-item:hover .tab-item-label,
.settings-tab-item.active .tab-item-label {
  color: white;
}

.settings-content {
  padding: 32px;
}

.settings-content-header {
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

.content-title {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.content-description {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.settings-content-body {
  max-height: 70vh;
  overflow-y: auto;
}

/* Settings Components Common Styles */
.settings-section {
  margin-bottom: 32px;
  padding: 24px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-bottom: 16px;
}

.settings-actions {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e2e8f0;
  text-align: right;
}

.settings-loading,
.settings-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #718096;
  text-align: center;
}

.settings-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  margin-bottom: 20px;
}

/* Table Management Styles */
.table-management {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.table-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

.table-management-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.table-management-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #718096;
}

.table-management-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  margin-bottom: 20px;
}

.table-form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-form-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.table-form-container {
  background: white;
  border-radius: 16px;
  width: 500px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  position: relative;
  z-index: 1001;
}

.table-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.table-form-header h3 {
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.table-form {
  padding: 24px;
}

/* Table Management Controls */
.tables-controls {
  background: white;
  border-radius: 12px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.search-section {
  margin-bottom: 20px;
}

.search-input-container {
  position: relative;
  max-width: 400px;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-icon {
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: #718096;
  font-size: 16px;
}

.filter-section {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: end;
  margin-bottom: 20px;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.filter-label {
  font-size: 12px;
  font-weight: 600;
  color: #4a5568;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.filter-select {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  min-width: 120px;
}

.filter-select:focus {
  outline: none;
  border-color: #667eea;
}

.view-toggle {
  display: flex;
  border: 2px solid #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
}

.view-btn {
  padding: 8px 12px;
  border: none;
  background: white;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
}

.view-btn:hover {
  background: #f7fafc;
}

.view-btn.active {
  background: #667eea;
  color: white;
}

.tables-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
  font-size: 14px;
  color: #718096;
}

.results-count {
  font-weight: 500;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  background: white;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-weight: 500;
  color: #4a5568;
}

/* Tables Grid Layouts */
.tables-grid {
  display: grid;
  gap: 20px;
}

.tables-grid.grid {
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
}

.tables-grid.list {
  grid-template-columns: 1fr;
  gap: 12px;
}

.empty-tables {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: #718096;
}

.empty-tables .empty-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
}

.empty-tables h3 {
  font-size: 20px;
  color: #2d3748;
  margin-bottom: 8px;
}

.table-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.table-card.list {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  gap: 20px;
}

.table-card.list .table-card-header {
  margin-bottom: 0;
  min-width: 120px;
}

.table-card.list .table-card-body {
  flex: 1;
  margin-bottom: 0;
  display: flex;
  gap: 24px;
}

.table-card.list .table-info {
  margin-bottom: 0;
}

.table-card.list .table-card-actions {
  border-top: none;
  padding-top: 0;
  margin-left: auto;
}

.table-card.list .status-buttons {
  margin-bottom: 0;
  margin-right: 16px;
}

.table-card.list .action-buttons {
  display: flex;
  gap: 8px;
}

/* Responsive Design for Tables */
@media (max-width: 768px) {
  .tables-controls {
    padding: 16px;
  }

  .filter-section {
    flex-direction: column;
    gap: 12px;
  }

  .filter-group {
    width: 100%;
  }

  .filter-select {
    width: 100%;
  }

  .tables-info {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .pagination {
    justify-content: center;
  }

  .tables-grid.grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  }

  .table-card.list {
    flex-direction: column;
    gap: 12px;
  }

  .table-card.list .table-card-header {
    min-width: auto;
  }

  .table-card.list .table-card-body {
    flex-direction: column;
    gap: 12px;
  }

  .table-card.list .table-card-actions {
    margin-left: 0;
  }

  .table-card.list .status-buttons {
    margin-right: 0;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .tables-grid.grid {
    grid-template-columns: 1fr;
  }

  .search-input-container {
    max-width: 100%;
  }

  .action-buttons {
    flex-wrap: wrap;
    gap: 6px;
  }

  .action-buttons button {
    font-size: 12px;
    padding: 6px 8px;
  }
}

.table-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.table-card.status-available {
  border-color: #22c55e;
}

.table-card.status-occupied {
  border-color: #ef4444;
}

.table-card.status-reserved {
  border-color: #f59e0b;
}

.table-card.status-maintenance {
  border-color: #6b7280;
}

.table-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.table-number {
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
}

.table-status {
  font-size: 14px;
  font-weight: 600;
}

.table-card-body {
  margin-bottom: 16px;
}

.table-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.info-label {
  font-weight: 600;
  color: #4a5568;
}

.info-value {
  color: #2d3748;
}

.table-card-actions {
  border-top: 1px solid #e2e8f0;
  padding-top: 16px;
}

.status-buttons {
  display: flex;
  gap: 8px;
  margin-bottom: 12px;
}

.status-btn {
  padding: 6px 8px;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 16px;
}

.status-btn:hover {
  background: #f7fafc;
}

.status-btn.active {
  background: #f0f9ff;
  border-color: currentColor;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.action-buttons button {
  flex: 1;
  padding: 8px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.edit-btn {
  background: #bee3f8;
  color: #2a69ac;
}

.delete-btn {
  background: #fed7d7;
  color: #742a2a;
}

.action-buttons button:hover {
  transform: translateY(-1px);
  opacity: 0.9;
}

/* Tax Management Styles */
.tax-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

/* Common Taxes Section */
.common-taxes {
  margin-bottom: 32px;
  background: white;
  border-radius: 16px;
  padding: 32px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
  border: 1px solid #e2e8f0;
}

.common-taxes-header {
  margin-bottom: 24px;
}

.common-taxes-header h4 {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8px;
}

.common-taxes-description {
  color: #718096;
  font-size: 14px;
  line-height: 1.5;
}

.common-tax-categories {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.tax-category {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.category-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.category-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.common-tax-btn {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  color: #2d3748;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.common-tax-btn:hover:not(:disabled) {
  border-color: #667eea;
  background: #f7faff;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.common-tax-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  background: #f1f5f9;
  border-color: #cbd5e0;
}

.tax-btn-content {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.tax-btn-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.tax-btn-rate {
  font-weight: 700;
  color: #667eea;
  font-size: 16px;
}

.added-icon {
  color: #48bb78;
}

.tax-form-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tax-form-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.tax-form-container {
  background: white;
  border-radius: 16px;
  width: 500px;
  max-width: 90vw;
  max-height: 95vh;
  min-height: 600px;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  position: relative;
  z-index: 1001;
  display: flex;
  flex-direction: column;
}

.tax-form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.tax-form-header h3 {
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.tax-form {
  padding: 24px;
  flex: 1;
  overflow-y: auto;
}

.tax-form .form-group {
  margin-bottom: 24px;
}

.tax-form .checkbox-label {
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
  transition: all 0.2s ease;
}

.tax-form .checkbox-label:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.tax-rates-list {
  margin-top: 24px;
}

.empty-taxes {
  text-align: center;
  padding: 60px 20px;
  color: #718096;
}

.empty-taxes .empty-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
}

.empty-taxes h3 {
  font-size: 20px;
  color: #2d3748;
  margin-bottom: 8px;
}

.tax-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 16px;
}

.tax-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.tax-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.tax-card.default {
  border-color: #f59e0b;
  background: #fffbeb;
}

.tax-card.inactive {
  opacity: 0.6;
  background: #f7fafc;
}

.tax-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.tax-name {
  font-size: 16px;
  font-weight: 700;
  color: #2d3748;
  flex: 1;
}

.tax-badges {
  display: flex;
  gap: 4px;
  flex-direction: column;
  align-items: flex-end;
}

.badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.default-badge {
  background: #fef3c7;
  color: #92400e;
}

.inactive-badge {
  background: #f3f4f6;
  color: #6b7280;
}

.tax-card-body {
  text-align: center;
  margin-bottom: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.tax-rate {
  font-size: 24px;
  font-weight: 800;
  color: #2d3748;
  margin-bottom: 4px;
}

.tax-type {
  font-size: 12px;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.tax-card-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.tax-card-actions button {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 80px;
}

.status-btn.active {
  background: #dcfce7;
  color: #166534;
}

.status-btn.inactive {
  background: #fef2f2;
  color: #991b1b;
}

.default-btn {
  background: #fef3c7;
  color: #92400e;
}

.tax-card-actions button:hover {
  transform: translateY(-1px);
  opacity: 0.9;
}

/* POS Pax Selection Styles */
.pax-selection {
  margin-top: 20px;
  padding: 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: #f8fafc;
}

.pax-selection h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
}

.pax-controls {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin-bottom: 12px;
}

.pax-btn {
  width: 40px;
  height: 40px;
  border: 2px solid #667eea;
  background: white;
  color: #667eea;
  border-radius: 8px;
  font-size: 18px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pax-btn:hover:not(:disabled) {
  background: #667eea;
  color: white;
}

.pax-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pax-count {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  min-width: 40px;
  text-align: center;
}

.pax-info {
  font-size: 12px;
  color: #718096;
  text-align: center;
  margin-bottom: 16px;
}

.confirm-table-btn {
  width: 100%;
  padding: 12px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-table-btn:hover {
  background: #5a67d8;
}

.table-option.selected {
  border-color: #667eea;
  background: #edf2f7;
}

/* Discount Section Styles */
.discount-section {
  margin-bottom: 20px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  background: #f8fafc;
}

.discount-section h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 12px;
}

.discount-controls {
  display: flex;
  gap: 12px;
  align-items: center;
}

.discount-type-select {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  min-width: 140px;
}

.discount-value-input {
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  width: 120px;
}

.summary-row.discount {
  color: #059669;
}

/* Customer Info Styles */
.customer-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
}

.form-textarea {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
}

.pax-info-display {
  margin-top: 12px;
  padding: 8px 12px;
  background: #edf2f7;
  border-radius: 6px;
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

/* Delivery Info Modal Styles */
.delivery-info-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.delivery-info-modal .modal-content {
  background: white;
  border-radius: 16px;
  padding: 0;
  width: 90%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 25px rgba(0, 0, 0, 0.1);
}

.delivery-form {
  padding: 24px;
}

.delivery-form .form-group {
  margin-bottom: 20px;
}

.delivery-form .form-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
}

.delivery-form .form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.delivery-form .form-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.delivery-form .form-textarea {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
  transition: border-color 0.2s ease;
}

.delivery-form .form-textarea:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modal-actions {
  padding: 20px 24px;
  border-top: 1px solid #e5e7eb;
  display: flex;
  gap: 12px;
  justify-content: flex-end;
}

.modal-actions .btn {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.modal-actions .btn-secondary {
  background: #f3f4f6;
  color: #374151;
}

.modal-actions .btn-secondary:hover {
  background: #e5e7eb;
}

.modal-actions .btn-primary {
  background: #667eea;
  color: white;
}

.modal-actions .btn-primary:hover:not(:disabled) {
  background: #5a67d8;
}

.modal-actions .btn-primary:disabled {
  background: #d1d5db;
  color: #9ca3af;
  cursor: not-allowed;
}

/* Table Dropdown Styles */
.table-dropdown {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.table-dropdown:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.table-dropdown option {
  padding: 8px;
}

/* Modal Pax Controls */
.dine-in-info {
  margin-top: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.table-pax-info {
  font-size: 14px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 12px;
}

.pax-controls-modal {
  margin-top: 12px;
}

.pax-controls-modal label {
  display: block;
  font-size: 13px;
  font-weight: 500;
  color: #6b7280;
  margin-bottom: 8px;
}

.pax-controls-modal .pax-controls {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
}

.pax-controls-modal .pax-btn {
  width: 32px;
  height: 32px;
  border: 1px solid #d1d5db;
  background: white;
  color: #374151;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pax-controls-modal .pax-btn:hover:not(:disabled) {
  background: #f3f4f6;
  border-color: #9ca3af;
}

.pax-controls-modal .pax-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.pax-controls-modal .pax-count {
  font-size: 16px;
  font-weight: 600;
  color: #374151;
  min-width: 24px;
  text-align: center;
}

.pax-controls-modal .pax-info {
  font-size: 12px;
  color: #6b7280;
  margin: 0;
}

/* Compact Table Cards */
.table-card-compact {
  background: white;
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
  cursor: pointer;
  position: relative;
  min-height: 100px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

.table-card-compact:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.table-card-compact.status-available {
  border-color: #22c55e;
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
}

.table-card-compact.status-occupied {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fee2e2 100%);
}

.table-card-compact.status-reserved {
  border-color: #f59e0b;
  background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.table-card-compact.status-maintenance {
  border-color: #6b7280;
  background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%);
}

.table-card-content {
  flex: 1;
}

.table-card-compact .table-number {
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 4px;
}

.table-card-compact .table-pax {
  font-size: 12px;
  color: #667eea;
  font-weight: 500;
  margin-bottom: 2px;
}

.table-card-compact .table-area {
  font-size: 11px;
  color: #718096;
  font-weight: 400;
}

.table-menu {
  position: absolute;
  top: 8px;
  right: 8px;
}

.menu-trigger {
  background: none;
  border: none;
  font-size: 16px;
  color: #6b7280;
  cursor: pointer;
  padding: 4px 8px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.menu-trigger:hover {
  background: rgba(0, 0, 0, 0.1);
  color: #374151;
}

.menu-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 10;
  min-width: 120px;
}

.menu-item {
  display: block;
  width: 100%;
  padding: 8px 12px;
  background: none;
  border: none;
  text-align: left;
  font-size: 13px;
  color: #374151;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.menu-item:hover {
  background: #f3f4f6;
}

.menu-item.delete {
  color: #dc2626;
}

.menu-item.delete:hover {
  background: #fef2f2;
}

.menu-item:first-child {
  border-radius: 8px 8px 0 0;
}

.menu-item:last-child {
  border-radius: 0 0 8px 8px;
}

/* Update tables grid for compact cards */
.tables-grid.grid {
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 16px;
}

/* Dashboard Overview Styles */
.dashboard-overview {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.overview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.overview-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.refresh-btn {
  padding: 8px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

/* Professional Dashboard Overview */
.dashboard-overview {
  background: #f8fafc;
  min-height: 100%;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
  padding: 24px;
}

.overview-title {
  font-size: 16px;
  font-weight: 400;
  color: #718096;
  margin: 0;
}

.metric-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.metric-card.sales {
  border-color: #48bb78;
  background: white;
}

.metric-card.sales .card-icon {
  background: linear-gradient(135deg, #48bb78 0%, #38a169 100%);
}

.metric-card.orders {
  border-color: #3182ce;
  background: white;
}

.metric-card.orders .card-icon {
  background: linear-gradient(135deg, #3182ce 0%, #2c5aa0 100%);
}

.metric-card.average {
  border-color: #ed8936;
  background: white;
}

.metric-card.average .card-icon {
  background: linear-gradient(135deg, #ed8936 0%, #dd6b20 100%);
}

.metric-card.menu {
  border-color: #8b5cf6;
  background: linear-gradient(135deg, #faf5ff 0%, #f3e8ff 100%);
}

.metric-card.tables {
  border-color: #ef4444;
  background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
}

.metric-card.taxes {
  border-color: #06b6d4;
  background: linear-gradient(135deg, #f0fdfa 0%, #ccfbf1 100%);
}

.metric-card.dinein {
  border-color: #8b5cf6;
  background: white;
}

.metric-card.dinein .card-icon {
  background: linear-gradient(135deg, #8b5cf6 0%, #7c3aed 100%);
}

.metric-card.takeaway {
  border-color: #f59e0b;
  background: white;
}

.metric-card.takeaway .card-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
}

.metric-card.delivery {
  border-color: #10b981;
  background: white;
}

.metric-card.delivery .card-icon {
  background: linear-gradient(135deg, #10b981 0%, #**********%);
}

.breakdown-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  margin-bottom: 32px;
  padding: 0 24px;
}

.breakdown-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 2px solid #e2e8f0;
}

.breakdown-card h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background: #f8fafc;
}

.status-item.available {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
}

.status-item.occupied {
  background: #fef2f2;
  border: 1px solid #fecaca;
}

.status-item.reserved {
  background: #fffbeb;
  border: 1px solid #fed7aa;
}

.status-item.maintenance {
  background: #f9fafb;
  border: 1px solid #d1d5db;
}

.status-icon {
  font-size: 16px;
}

.status-label {
  flex: 1;
  font-weight: 500;
  color: #374151;
}

.status-count {
  font-weight: 700;
  color: #1f2937;
}

.quick-actions {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.action-btn {
  padding: 16px;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 12px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-align: center;
  color: #4a5568;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  min-height: 60px;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
  color: #667eea;
}

.menu-btn:hover {
  border-color: #8b5cf6;
  background: #faf5ff;
  color: #8b5cf6;
}

.table-btn:hover {
  border-color: #3182ce;
  background: #eff6ff;
  color: #3182ce;
}

.pos-btn:hover {
  border-color: #48bb78;
  background: #f0fdf4;
  color: #48bb78;
}

.settings-btn:hover {
  border-color: #718096;
  background: #f7fafc;
  color: #718096;
}

.table-btn:hover {
  border-color: #ef4444;
  background: #fef2f2;
}

.pos-btn:hover {
  border-color: #3b82f6;
  background: #eff6ff;
}

.settings-btn:hover {
  border-color: #6b7280;
  background: #f9fafb;
}

.activity-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 2px solid #e2e8f0;
}

.activity-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 8px;
  background: #f8fafc;
}

.activity-item.warning {
  background: #fffbeb;
  border: 1px solid #fed7aa;
}

.activity-item.info {
  background: #eff6ff;
  border: 1px solid #bfdbfe;
}

.activity-icon {
  font-size: 16px;
}

.activity-text {
  flex: 1;
  font-weight: 500;
  color: #374151;
}

.activity-time {
  font-size: 12px;
  color: #6b7280;
  font-weight: 600;
}

.dashboard-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #718096;
}

.dashboard-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  margin-bottom: 20px;
}

/* User Profile Styles */
.user-profile {
  max-width: 1000px;
  margin: 0 auto;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  margin-bottom: 32px;
  color: white;
}

.profile-avatar {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.3);
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-icon {
  font-size: 32px;
  color: rgba(255, 255, 255, 0.8);
}

.profile-info {
  flex: 1;
}

.profile-name {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 4px 0;
}

.profile-role {
  font-size: 14px;
  opacity: 0.9;
  margin: 0 0 4px 0;
}

.profile-details {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.profile-id {
  font-size: 12px;
  opacity: 0.7;
  margin: 0;
  font-family: monospace;
}

.trial-info {
  font-size: 12px;
  opacity: 0.8;
  margin: 4px 0 0 0;
  font-style: italic;
}

.profile-actions {
  display: flex;
  gap: 12px;
}

.edit-actions {
  display: flex;
  gap: 12px;
}

.profile-sections {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-display {
  padding: 12px 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  color: #2d3748;
  min-height: 20px;
}

.form-display.readonly {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px 20px;
  color: #475569;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 15px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.form-grid .full-width {
  grid-column: 1 / -1;
}

.security-info {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.security-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.security-icon {
  font-size: 24px;
  width: 40px;
  text-align: center;
}

.security-content {
  flex: 1;
}

.security-content h4 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 4px 0;
}

.security-content p {
  font-size: 14px;
  color: #718096;
  margin: 0;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-badge.active {
  background: #c6f6d5;
  color: #22543d;
}

.status-badge.trial {
  background: #fef5e7;
  color: #c05621;
}

.status-badge.expired {
  background: #fed7d7;
  color: #c53030;
}

.status-badge.cancelled {
  background: #e2e8f0;
  color: #4a5568;
}

/* Toast Notification Styles */
.toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  min-width: 300px;
  max-width: 500px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  animation: slideInRight 0.3s ease-out;
}

.toast-success {
  background: #f0fff4;
  border: 1px solid #9ae6b4;
  color: #22543d;
}

.toast-error {
  background: #fff5f5;
  border: 1px solid #feb2b2;
  color: #c53030;
}

.toast-info {
  background: #ebf8ff;
  border: 1px solid #90cdf4;
  color: #2c5282;
}

.toast-content {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 8px;
}

.toast-icon {
  font-size: 16px;
  flex-shrink: 0;
}

.toast-message {
  flex: 1;
  font-size: 14px;
  font-weight: 500;
}

.toast-close {
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  transition: opacity 0.2s;
}

.toast-close:hover {
  opacity: 1;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Form Error Styles */
.form-input.error,
.form-select.error {
  border-color: #e53e3e;
  box-shadow: 0 0 0 1px #e53e3e;
}

.error-message {
  display: block;
  color: #e53e3e;
  font-size: 12px;
  margin-top: 4px;
  font-weight: 500;
}

/* Help Text */
.help-text {
  display: block;
  color: #718096;
  font-size: 11px;
  margin-top: 2px;
  font-style: italic;
}

/* Website Link */
.website-link {
  color: #3182ce;
  text-decoration: none;
  transition: color 0.2s;
}

.website-link:hover {
  color: #2c5282;
  text-decoration: underline;
}

/* Machine Code Display */
.machine-code {
  font-family: monospace;
  background: #f7fafc;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  color: #2d3748;
}

/* Subscription Info Styles - Removed duplicate styles that conflict with sidebar dark theme */

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.detail-label {
  font-size: 14px;
  color: #718096;
  font-weight: 500;
}

.detail-value {
  font-size: 14px;
  color: #2d3748;
  font-weight: 600;
}

/* Professional POS Styles */
.professional-pos {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
}

/* Professional POS Header */
.pos-header-section {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 24px 32px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.pos-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.pos-main-title {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  line-height: 1.2;
}

.pos-subtitle {
  font-size: 14px;
  color: #718096;
  margin: 0;
  font-weight: 400;
}

.pos-controls {
  display: flex;
  align-items: center;
  gap: 24px;
}

.selected-table-info {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f0f4ff;
  padding: 8px 16px;
  border-radius: 8px;
  border: 1px solid #667eea;
  color: #667eea;
  font-size: 14px;
  font-weight: 600;
}

.pos-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: white;
  border-bottom: 2px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.pos-header h2 {
  font-size: 24px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.order-type-selector {
  display: flex;
  gap: 8px;
}

.order-type-btn {
  padding: 12px 20px;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
  color: #4a5568;
}

.order-type-btn:hover {
  border-color: #667eea;
  background: #f0f4ff;
  color: #667eea;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.15);
}

.order-type-btn.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.pos-content {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  padding: 24px;
  overflow: hidden;
}

.menu-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
}

.menu-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  padding: 20px;
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border-radius: 16px;
  border: 1px solid #e2e8f0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.search-section {
  display: flex;
  gap: 12px;
  flex: 1;
}

.view-controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.view-mode-toggle {
  display: flex;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  overflow: hidden;
}

.view-btn {
  padding: 8px 12px;
  border: none;
  background: white;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s ease;
}

.view-btn:hover {
  background: #f0f4ff;
}

.view-btn.active {
  background: #667eea;
  color: white;
}

.items-per-page-select {
  padding: 8px 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 12px;
  background: white;
}

.menu-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding: 0 4px;
}

.items-count {
  font-size: 14px;
  color: #4a5568;
  font-weight: 500;
}

.pagination {
  display: flex;
  align-items: center;
  gap: 12px;
}

.page-btn {
  padding: 6px 12px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 600;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: #f0f4ff;
  border-color: #667eea;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  font-size: 12px;
  color: #4a5568;
  font-weight: 600;
}

.search-input {
  flex: 1;
  padding: 14px 18px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 14px;
  background: white;
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  transform: translateY(-1px);
}

.category-select {
  padding: 14px 18px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 14px;
  background: white;
  min-width: 180px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.category-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.menu-items-container {
  overflow-y: auto;
  flex: 1;
  padding: 8px;
}

.menu-items-container.grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 12px;
}

.menu-items-container.list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.menu-item-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.menu-item-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.menu-item-card.grid {
  display: flex;
  flex-direction: column;
  height: 220px;
}

.menu-item-card.list {
  display: flex;
  flex-direction: row;
  height: 80px;
  align-items: center;
}

.item-image-container {
  position: relative;
  overflow: hidden;
}

.menu-item-card.grid .item-image-container {
  height: 120px;
  margin-bottom: 12px;
}

.menu-item-card.list .item-image-container {
  width: 80px;
  height: 80px;
  flex-shrink: 0;
  margin-right: 12px;
}

.item-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.menu-item-card:hover .item-image {
  transform: scale(1.05);
}

.item-image-placeholder {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.placeholder-icon {
  font-size: 24px;
  opacity: 0.5;
}

.menu-item-card.list .placeholder-icon {
  font-size: 20px;
}

.item-info {
  flex: 1;
  padding: 12px;
}

.menu-item-card.grid .item-info {
  text-align: center;
  padding: 0 8px 12px 8px;
}

.menu-item-card.list .item-info {
  text-align: left;
  padding: 8px 12px;
}

.item-name {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 6px 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.menu-item-card.list .item-name {
  font-size: 15px;
  -webkit-line-clamp: 1;
}

.item-price {
  font-size: 16px;
  font-weight: 700;
  color: #667eea;
  margin: 0 0 6px 0;
}

.menu-item-card.list .item-price {
  font-size: 14px;
}

.item-description {
  font-size: 11px;
  color: #718096;
  margin: 0;
  line-height: 1.3;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.menu-item-card.list .item-description {
  font-size: 12px;
  -webkit-line-clamp: 1;
}

.item-details {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-category {
  font-size: 10px;
  color: #667eea;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.add-btn {
  position: absolute;
  width: 28px;
  height: 28px;
  border-radius: 50%;
  border: none;
  background: #667eea;
  color: white;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.menu-item-card.grid .add-btn {
  top: 8px;
  right: 8px;
}

.menu-item-card.list .add-btn {
  top: 50%;
  right: 12px;
  transform: translateY(-50%);
}

.add-btn:hover {
  background: #5a67d8;
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(102, 126, 234, 0.4);
}

.menu-item-card.list .add-btn:hover {
  transform: translateY(-50%) scale(1.1);
}

.no-items {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: #718096;
}

.no-items-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8fafc;
  border-radius: 50%;
  color: #718096;
}

.no-items h3 {
  font-size: 18px;
  color: #2d3748;
  margin-bottom: 8px;
}

.no-items p {
  font-size: 14px;
  margin: 0;
}

.menu-loading {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #718096;
}

.menu-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.menu-loading p {
  font-size: 14px;
  margin: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .menu-controls {
    flex-direction: column;
    gap: 12px;
  }

  .search-section {
    width: 100%;
  }

  .view-controls {
    justify-content: space-between;
    width: 100%;
  }

  .menu-items-container.grid {
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 8px;
  }

  .menu-item-card.grid {
    height: 180px;
  }

  .menu-item-card.grid .item-image-container {
    height: 100px;
  }

  .item-name {
    font-size: 12px;
  }

  .item-price {
    font-size: 14px;
  }

  .item-description {
    font-size: 10px;
  }
}

/* Performance optimizations */
.menu-item-card {
  will-change: transform;
}

.item-image {
  will-change: transform;
}

/* Simplified POS Styles */
.simplified-pos {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  overflow: hidden;
}

.simplified-pos-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  color: #718096;
}

.simplified-pos-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20px;
}

/* POS Header */
.pos-header {
  background: white;
  border-bottom: 1px solid #e2e8f0;
  padding: 16px 24px;
  display: flex;
  align-items: center;
  gap: 24px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  flex-shrink: 0;
}

.order-type-tabs {
  display: flex;
  gap: 8px;
}

.order-type-tab {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  background: white;
  color: #4a5568;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.order-type-tab:hover {
  border-color: #cbd5e0;
  background: #f7fafc;
}

.order-type-tab.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.table-selector {
  flex: 1;
  max-width: 300px;
}

.visual-table-btn {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  background: white;
  color: #2d3748;
  cursor: pointer;
  transition: all 0.2s ease;
}

.visual-table-btn:hover {
  border-color: #cbd5e0;
  background: #f7fafc;
}

.visual-table-btn:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.visual-table-btn span {
  flex: 1;
  text-align: left;
}

/* Legacy table dropdown (fallback) */
.table-dropdown {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  background: white;
  color: #2d3748;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.table-dropdown:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.customer-info {
  display: flex;
  gap: 12px;
  flex: 1;
  background: #f8fafc;
  padding: 16px;
  border-radius: 12px;
  border: 2px solid #e2e8f0;
  margin-top: 8px;
  position: relative;
}

.customer-info::before {
  position: absolute;
  top: -12px;
  left: 16px;
  background: #f8fafc;
  padding: 0 8px;
  font-size: 12px;
  font-weight: 600;
  color: #374151;
}

/* Default content for delivery */
.customer-info::before {
  content: "📍 Delivery Information - All fields required";
}

/* Content for takeaway */
.order-type-tab.active[data-type="takeaway"] ~ .pos-main .customer-info::before,
.simplified-pos[data-order-type="takeaway"] .customer-info::before {
  content: "🥡 Takeaway Information - Optional";
}

/* Content for dine-in */
.order-type-tab.active[data-type="dine-in"] ~ .pos-main .customer-info::before,
.simplified-pos[data-order-type="dine-in"] .customer-info::before {
  content: "🍽️ Customer Information - Optional";
}

/* POS Window Navigation */
.pos-navigation {
  display: flex;
  background: #f8fafc;
  border-bottom: 2px solid #e2e8f0;
  padding: 0;
  margin: 0;
}

.nav-btn {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 16px 24px;
  background: transparent;
  border: none;
  font-size: 14px;
  font-weight: 600;
  color: #64748b;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 3px solid transparent;
}

.nav-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.nav-btn.active {
  background: white;
  color: #667eea;
  border-bottom-color: #667eea;
}

.nav-btn.active:hover {
  background: white;
}

/* Order Type Badge */
.order-type-badge {
  display: inline-block;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: #f1f5f9;
  color: #475569;
  border: 1px solid #e2e8f0;
}

/* Area Selection Components */
.area-selection-container {
  position: relative;
}

.add-area-input-container {
  margin-top: 8px;
  padding: 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
}

.add-area-buttons {
  display: flex;
  gap: 8px;
  margin-top: 8px;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 12px;
}

/* Readonly Form Fields */
.form-display.readonly {
  background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px 20px;
  color: #475569;
  position: relative;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-size: 15px;
  font-weight: 500;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
  transition: all 0.2s ease;
}

.form-display.readonly:hover {
  border-color: #cbd5e1;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.04);
}

.readonly-note {
  display: block;
  font-size: 12px;
  color: #64748b;
  margin-top: 6px;
  font-style: italic;
  font-weight: 400;
  padding: 4px 8px;
  background: rgba(100, 116, 139, 0.1);
  border-radius: 6px;
  border-left: 3px solid #94a3b8;
}

/* Form Notes */
.form-note {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-top: 6px;
  padding: 8px 12px;
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  font-size: 12px;
  color: #0369a1;
}

.note-icon {
  font-size: 14px;
}

.customer-input {
  flex: 1;
  min-width: 180px;
  padding: 14px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  background: white;
  transition: all 0.2s ease;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
}

.customer-input:focus {
  outline: none;
  border-color: #10b981;
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
  transform: translateY(-1px);
}

.customer-input::placeholder {
  color: #9ca3af;
  font-weight: 400;
}

.customer-input:invalid:not(:placeholder-shown) {
  border-color: #ef4444;
}

.customer-input:valid:not(:placeholder-shown) {
  border-color: #10b981;
}

.help-btn {
  width: 44px;
  height: 44px;
  border: 2px solid #e2e8f0;
  border-radius: 50%;
  background: white;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.help-btn:hover {
  border-color: #667eea;
  color: #667eea;
  background: #f0f4ff;
  transform: scale(1.05);
}

/* Main Content Area */
.pos-main {
  flex: 1;
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  padding: 24px;
  min-height: 0;
  overflow: hidden;
}

/* Menu Section */
.menu-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.menu-controls {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
  flex-shrink: 0;
}

.search-bar {
  position: relative;
  display: flex;
  align-items: center;
}

.search-bar .icon {
  position: absolute;
  left: 16px;
  color: #a0aec0;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 16px 48px 16px 48px;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  font-size: 16px;
  background: #f8fafc;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input::placeholder {
  color: #a0aec0;
}

.clear-search {
  position: absolute;
  right: 16px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  color: #a0aec0;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: #e2e8f0;
  color: #4a5568;
}

.category-filter {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.category-btn {
  padding: 8px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 20px;
  background: white;
  color: #4a5568;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.category-btn:hover {
  border-color: #cbd5e0;
  background: #f7fafc;
}

.category-btn.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

/* Menu Grid Container */
.menu-grid-container {
  flex: 1;
  min-height: 0;
  position: relative;
}

/* Virtualized Menu Grid */
.virtualized-menu-grid {
  height: 100%;
  overflow-y: auto;
  padding-right: 8px;
}

.virtualized-menu-grid::-webkit-scrollbar {
  width: 6px;
}

.virtualized-menu-grid::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.virtualized-menu-grid::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.virtualized-menu-grid::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

/* Legacy Menu Grid (fallback) */
.menu-grid {
  flex: 1;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
  gap: 16px;
  overflow-y: auto;
  padding-right: 8px;
}

.menu-grid::-webkit-scrollbar {
  width: 6px;
}

.menu-grid::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.menu-grid::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.menu-grid::-webkit-scrollbar-thumb:hover {
  background: #a0aec0;
}

.empty-menu {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: #718096;
}

.empty-menu .icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-menu h3 {
  font-size: 20px;
  color: #2d3748;
  margin-bottom: 8px;
}

.empty-menu p {
  margin: 0;
}

/* Menu Item Cards */
.menu-item-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  height: 200px;
  display: flex;
  flex-direction: column;
}

.menu-item-card:hover {
  border-color: #667eea;
  transform: translateY(-4px);
  box-shadow: 0 12px 30px rgba(102, 126, 234, 0.2);
}

.menu-item-card:hover .add-overlay {
  opacity: 1;
}

.menu-item-card.virtualized {
  position: absolute;
  will-change: transform;
}

.item-image {
  position: relative;
  height: 120px;
  overflow: hidden;
  background: #f8fafc;
}

.menu-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.menu-item-card:hover .menu-image {
  transform: scale(1.05);
}

.add-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(102, 126, 234, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.add-overlay .icon {
  color: white;
  font-size: 32px;
}

.item-info {
  padding: 16px;
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  text-align: center;
}

.item-name {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.item-price {
  font-size: 16px;
  font-weight: 700;
  color: #667eea;
  margin: 0;
}

/* Order Section */
.order-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  border: 1px solid #e2e8f0;
  display: flex;
  flex-direction: column;
  min-height: 0;
}

.order-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
  flex-shrink: 0;
}

.order-title-section {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.order-header h3 {
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.item-count {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
}

.order-actions-header {
  display: flex;
  gap: 8px;
}

.quick-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  background: white;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-btn:hover {
  background: #f7fafc;
  border-color: #cbd5e0;
}

.quick-btn.duplicate-btn:hover {
  border-color: #3182ce;
  color: #3182ce;
}

.quick-btn.clear-btn:hover {
  border-color: #e53e3e;
  color: #e53e3e;
  background: #fed7d7;
}

.order-items {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
  min-height: 0;
}

.order-items::-webkit-scrollbar {
  width: 6px;
}

.order-items::-webkit-scrollbar-track {
  background: #f1f5f9;
  border-radius: 3px;
}

.order-items::-webkit-scrollbar-thumb {
  background: #cbd5e0;
  border-radius: 3px;
}

.empty-order {
  text-align: center;
  padding: 40px 20px;
  color: #718096;
}

.empty-order .icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-order p {
  margin: 0 0 8px 0;
  font-size: 16px;
}

.empty-subtitle {
  font-size: 14px !important;
  opacity: 0.7;
}

/* Order Items */
.order-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  margin-bottom: 12px;
  background: #f8fafc;
  transition: all 0.2s ease;
  position: relative;
}

.order-item:hover {
  background: white;
  border-color: #cbd5e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.item-index {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  font-size: 12px;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.item-details {
  flex: 1;
  min-width: 0;
}

.order-item .item-name {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 4px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.order-item .item-price {
  font-size: 12px;
  color: #718096;
  margin: 0;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 4px;
}

.qty-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f7fafc;
  color: #4a5568;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qty-btn:hover {
  background: #e2e8f0;
  color: #2d3748;
}

.qty-btn:active {
  transform: scale(0.95);
}

.qty-btn.decrease:hover {
  background: #fed7d7;
  color: #e53e3e;
}

.qty-btn.increase:hover {
  background: #c6f6d5;
  color: #38a169;
}

.remove-item-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #fed7d7;
  color: #e53e3e;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.remove-item-btn:hover {
  background: #fbb6ce;
  transform: scale(1.05);
}

.remove-item-btn:active {
  transform: scale(0.95);
}

.quantity {
  min-width: 32px;
  text-align: center;
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.item-total {
  font-size: 14px;
  font-weight: 700;
  color: #667eea;
  min-width: 60px;
  text-align: right;
}

/* Order Summary */
.order-summary {
  border-top: 2px solid #e2e8f0;
  padding-top: 20px;
  flex-shrink: 0;
}

.summary-line {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-size: 14px;
}

.summary-line span:first-child {
  color: #4a5568;
  font-weight: 500;
}

.summary-line span:last-child {
  color: #2d3748;
  font-weight: 600;
}

.summary-line.total {
  font-size: 18px;
  font-weight: 700;
  padding-top: 12px;
  border-top: 1px solid #e2e8f0;
  margin-bottom: 20px;
}

.summary-line.total span {
  color: #2d3748;
}

.create-order-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 16px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
}

.create-order-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
}

.create-order-btn:active {
  transform: translateY(0);
}

/* Touch-friendly improvements */
.menu-item-card,
.order-type-tab,
.visual-table-btn,
.qty-btn,
.create-order-btn,
.table-card {
  min-height: 44px; /* iOS recommended touch target size */
  touch-action: manipulation; /* Prevent double-tap zoom */
}

.search-input {
  font-size: 16px; /* Prevent zoom on iOS */
}

/* Tablet optimizations */
@media (max-width: 1024px) {
  .pos-main {
    grid-template-columns: 1fr;
    grid-template-rows: 1fr auto;
  }

  .order-section {
    max-height: 400px;
  }

  .menu-item-card {
    min-height: 48px;
  }

  .qty-btn {
    min-width: 44px;
    min-height: 44px;
  }

  .order-type-tab {
    padding: 16px 24px;
    font-size: 16px;
  }
}

@media (max-width: 768px) {
  .pos-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .order-type-tabs {
    justify-content: center;
  }

  .customer-info {
    flex-direction: column;
  }

  .menu-grid {
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 12px;
  }

  .menu-item-card {
    height: 160px;
  }

  .item-image {
    height: 100px;
  }

  .item-info {
    padding: 12px;
  }

  .item-name {
    font-size: 12px;
  }

  .item-price {
    font-size: 14px;
  }
}

@media (max-width: 480px) {
  .pos-main {
    padding: 16px;
    gap: 16px;
  }

  .menu-section,
  .order-section {
    padding: 16px;
  }

  .menu-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 8px;
  }

  .menu-item-card {
    height: 140px;
  }

  .item-image {
    height: 80px;
  }
}

/* Visual Table Selector Styles */
.visual-table-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.visual-table-selector {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  max-width: 800px;
  max-height: 80vh;
  width: 90vw;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.selector-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.selector-header h3 {
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.close-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 8px;
  background: #e2e8f0;
  color: #4a5568;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #cbd5e0;
  color: #2d3748;
}

.table-legend {
  display: flex;
  gap: 24px;
  padding: 16px 24px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #4a5568;
}

.legend-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
}

.legend-dot.available {
  background: #10b981;
}

.legend-dot.occupied {
  background: #ef4444;
}

.legend-dot.reserved {
  background: #f59e0b;
}

.legend-dot.maintenance {
  background: #6b7280;
}

.table-areas {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.table-area {
  margin-bottom: 32px;
}

.table-area:last-child {
  margin-bottom: 0;
}

.area-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.table-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 16px;
}

.table-card {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.table-card.selectable:hover {
  border-color: #667eea;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
}

.table-card.selected {
  border-color: #667eea;
  background: #f0f4ff;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.table-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.table-card.occupied {
  border-color: #fecaca;
  background: #fef2f2;
}

.table-card.reserved {
  border-color: #fde68a;
  background: #fffbeb;
}

.table-card.maintenance {
  border-color: #d1d5db;
  background: #f9fafb;
}

.table-number {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin-bottom: 12px;
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
}

.table-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.capacity {
  font-size: 12px;
  color: #718096;
  font-weight: 500;
}

.status {
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  padding: 2px 6px;
  border-radius: 4px;
}

.status.available {
  background: #d1fae5;
  color: #065f46;
}

.status.occupied {
  background: #fecaca;
  color: #991b1b;
}

.status.reserved {
  background: #fde68a;
  color: #92400e;
}

.status.maintenance {
  background: #f3f4f6;
  color: #374151;
}

.selected-table-info {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  background: #f0f4ff;
  border-top: 1px solid #e2e8f0;
}

.selected-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: 600;
  color: #2d3748;
}

.confirm-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.confirm-btn:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

/* Keyboard Shortcuts Help Modal */
.keyboard-shortcuts-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1001;
  backdrop-filter: blur(4px);
}

.keyboard-shortcuts-modal {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  max-width: 700px;
  max-height: 80vh;
  width: 90vw;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.shortcuts-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.shortcuts-header h3 {
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.shortcuts-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.shortcuts-intro {
  color: #4a5568;
  margin-bottom: 24px;
  font-size: 14px;
  line-height: 1.5;
}

.shortcuts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
  margin-bottom: 24px;
}

.shortcuts-category {
  background: #f8fafc;
  border-radius: 12px;
  padding: 20px;
  border: 1px solid #e2e8f0;
}

.category-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.shortcuts-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 16px;
}

.shortcut-key {
  display: flex;
  align-items: center;
  gap: 4px;
  min-width: 100px;
}

.shortcut-key kbd {
  background: #2d3748;
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 600;
  font-family: monospace;
  border: 1px solid #4a5568;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.shortcut-key .plus {
  color: #718096;
  font-weight: 600;
  font-size: 12px;
}

.shortcut-description {
  flex: 1;
  color: #4a5568;
  font-size: 14px;
}

.shortcuts-tip {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 8px;
  color: #92400e;
  font-size: 14px;
  line-height: 1.5;
}

.shortcuts-tip .icon {
  color: #f59e0b;
  flex-shrink: 0;
  margin-top: 2px;
}

.shortcuts-tip kbd {
  background: #92400e;
  color: #fef3c7;
  padding: 2px 6px;
  border-radius: 3px;
  font-size: 11px;
  font-weight: 600;
  font-family: monospace;
}

@media (max-width: 768px) {
  .shortcuts-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .shortcut-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .shortcut-key {
    min-width: auto;
  }
}

/* Performance Test Panel Styles */
.performance-test-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1002;
  backdrop-filter: blur(4px);
}

.performance-test-panel {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  max-width: 800px;
  max-height: 90vh;
  width: 90vw;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.test-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.test-header h3 {
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.test-content {
  flex: 1;
  overflow-y: auto;
  padding: 24px;
}

.test-description {
  margin-bottom: 24px;
  padding: 16px;
  background: #f0f4ff;
  border: 1px solid #c7d2fe;
  border-radius: 8px;
  color: #3730a3;
  font-size: 14px;
  line-height: 1.5;
}

.test-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
  margin-bottom: 24px;
}

.control-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.control-group label {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.control-input {
  padding: 12px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s ease;
}

.control-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.control-hint {
  font-size: 12px;
  color: #718096;
  font-style: italic;
}

.test-actions {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.generate-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.generate-btn:hover:not(:disabled) {
  background: #5a67d8;
  transform: translateY(-1px);
}

.generate-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.clear-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: white;
  color: #e53e3e;
  border: 2px solid #fed7d7;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-btn:hover:not(:disabled) {
  background: #fed7d7;
}

.clear-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.test-results {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 24px;
}

.test-results h4 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 16px 0;
}

.results-log {
  background: #1a202c;
  color: #e2e8f0;
  padding: 16px;
  border-radius: 6px;
  font-family: monospace;
  font-size: 12px;
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 16px;
}

.log-line {
  margin-bottom: 4px;
  line-height: 1.4;
}

.results-summary {
  background: white;
  padding: 16px;
  border-radius: 6px;
  border: 1px solid #e2e8f0;
}

.results-summary h5 {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 12px 0;
}

.results-summary ul {
  margin: 0;
  padding-left: 20px;
  color: #4a5568;
  font-size: 13px;
}

.results-summary li {
  margin-bottom: 4px;
}

.test-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.info-section {
  background: #f8fafc;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.info-section h5 {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 12px 0;
}

.info-section ul {
  margin: 0;
  padding-left: 20px;
  color: #4a5568;
  font-size: 13px;
}

.info-section li {
  margin-bottom: 6px;
}

@media (max-width: 768px) {
  .test-controls {
    grid-template-columns: 1fr;
  }

  .test-actions {
    flex-direction: column;
  }

  .test-info {
    grid-template-columns: 1fr;
  }
}

/* Smooth scrolling for menu container */
.menu-items-container {
  scroll-behavior: smooth;
}

/* Better focus states for accessibility */
.menu-item-card:focus {
  outline: 3px solid #667eea;
  outline-offset: 2px;
}

.add-btn:focus {
  outline: 2px solid #ffffff;
  outline-offset: 2px;
}

.order-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  display: flex;
  flex-direction: column;
  height: 100%;
  max-height: calc(100vh - 120px);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e2e8f0;
  flex-wrap: wrap;
  gap: 12px;
}

.order-header h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin: 0;
  flex: 1;
}

.selected-table {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.order-items {
  flex: 1;
  overflow-y: auto;
  margin-bottom: 20px;
  min-height: 0;
  max-height: calc(100vh - 400px);
}

.empty-order {
  text-align: center;
  padding: 40px 20px;
  color: #718096;
}

.empty-order p {
  margin: 0 0 8px 0;
}

.order-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  margin-bottom: 8px;
}

.order-item .item-details {
  flex: 1;
}

.order-item .item-name {
  font-weight: 600;
  color: #2d3748;
  display: block;
  margin-bottom: 4px;
}

.order-item .item-price {
  font-size: 12px;
  color: #718096;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.quantity-controls button {
  width: 24px;
  height: 24px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 600;
}

.quantity-controls button:hover {
  background: #f7fafc;
}

.quantity {
  font-weight: 600;
  min-width: 20px;
  text-align: center;
}

.item-subtotal {
  font-weight: 700;
  color: #667eea;
  min-width: 60px;
  text-align: right;
}

.remove-btn {
  width: 24px;
  height: 24px;
  border: none;
  background: #fed7d7;
  color: #742a2a;
  border-radius: 4px;
  cursor: pointer;
  font-weight: 700;
}

.remove-btn:hover {
  background: #feb2b2;
}

.order-summary {
  border-top: 1px solid #e2e8f0;
  padding: 16px 0;
  margin-bottom: 0;
  background: white;
  position: sticky;
  bottom: 60px;
  z-index: 5;
}

.summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.summary-row.total {
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
  border-top: 1px solid #e2e8f0;
  padding-top: 8px;
  margin-top: 8px;
}

/* Order Type and Table Selection */
.order-type-selector {
  display: flex;
  align-items: center;
}

.order-type-select {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  color: #2d3748;
  cursor: pointer;
  transition: all 0.2s ease;
}

.order-type-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.table-selection-modal .table-selection-section,
.billing-modal .table-selection-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
}

.table-selection-modal .table-selection-label,
.billing-modal .table-selection-label {
  display: block;
  font-size: 13px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 12px;
}

.table-selection-modal .table-selection-grid,
.billing-modal .table-selection-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(60px, 1fr));
  gap: 8px;
  margin-bottom: 12px;
}

.table-selection-modal .table-option-btn,
.billing-modal .table-option-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px 4px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
  min-height: 50px;
  color: rgba(255, 255, 255, 0.9);
}

.table-selection-modal .table-option-btn:hover,
.billing-modal .table-option-btn:hover {
  border-color: #667eea;
  background: rgba(102, 126, 234, 0.2);
}

.table-selection-modal .table-option-btn.selected,
.billing-modal .table-option-btn.selected {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.table-selection-modal .table-number,
.billing-modal .table-number {
  font-weight: 600;
  font-size: 12px;
}

.table-selection-modal .table-capacity,
.billing-modal .table-capacity {
  font-size: 10px;
  opacity: 0.8;
}

.table-selection-modal .table-option-btn.selected .table-capacity,
.billing-modal .table-option-btn.selected .table-capacity {
  opacity: 1;
}

.table-selection-modal .no-tables-available,
.billing-modal .no-tables-available {
  text-align: center;
  padding: 20px;
  color: #718096;
  font-size: 13px;
}

.selected-table-info {
  display: flex;
  align-items: center;
  gap: 8px;
  background: #f0fff4;
  border: 1px solid #9ae6b4;
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 12px;
  color: #22543d;
  font-weight: 500;
}

.billing-modal .customer-info-section {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.1);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
}

.billing-modal .customer-input-group {
  margin-bottom: 12px;
}

.billing-modal .customer-input-group:last-child {
  margin-bottom: 0;
}

.billing-modal .customer-input-group label {
  display: block;
  font-size: 12px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 6px;
}

.billing-modal .customer-input {
  width: 100%;
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid rgba(255, 255, 255, 0.2);
  border-radius: 8px;
  padding: 8px 12px;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.9);
  transition: all 0.2s ease;
}

.billing-modal .customer-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

.billing-modal .customer-input::placeholder {
  color: rgba(255, 255, 255, 0.5);
}

.order-actions {
  display: flex;
  gap: 12px;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
  background: white;
  position: sticky;
  bottom: 0;
  z-index: 10;
}

.order-actions .btn {
  flex: 1;
  padding: 12px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.order-actions .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.order-actions .btn-secondary {
  background: #f7fafc;
  border: 2px solid #e2e8f0;
  color: #4a5568;
}

.order-actions .btn-secondary:hover:not(:disabled) {
  background: #edf2f7;
}

.order-actions .btn-primary {
  background: #667eea;
  border: 2px solid #667eea;
  color: white;
}

.order-actions .btn-primary:hover:not(:disabled) {
  background: #5a67d8;
  border-color: #5a67d8;
}

/* POS Modal Styles */
.table-selection-modal,
.billing-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.modal-content {
  background: white;
  border-radius: 16px;
  width: 600px;
  max-width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.25);
  position: relative;
  z-index: 1001;
}

.modal-content.edit-order-modal.compact {
  width: 700px;
  max-width: 95vw;
}

.billing-content {
  width: 800px;
}

/* Edit Order Modal Styles */
.edit-order-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.current-items-section h3,
.add-items-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 2px solid #e2e8f0;
}

.order-items-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;
}

.order-item-edit {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.order-item-edit:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
}

.item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.item-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.item-price {
  color: #667eea;
  font-weight: 500;
  font-size: 13px;
}

.item-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.quantity-controls {
  display: flex;
  align-items: center;
  gap: 8px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 4px;
}

.qty-btn {
  width: 28px;
  height: 28px;
  border: none;
  background: #667eea;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.2s ease;
}

.qty-btn:hover {
  background: #5a67d8;
  transform: scale(1.05);
}

.quantity {
  min-width: 24px;
  text-align: center;
  font-weight: 600;
  color: #2d3748;
}

.item-subtotal {
  font-weight: 600;
  color: #2d3748;
  min-width: 80px;
  text-align: right;
}

.remove-btn {
  background: #fed7d7;
  border: 1px solid #feb2b2;
  color: #c53030;
  border-radius: 8px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.remove-btn:hover {
  background: #feb2b2;
  border-color: #fc8181;
}

/* Search Interface Styles */
.search-container {
  margin-bottom: 16px;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 12px 16px;
  transition: all 0.2s ease;
}

.search-input-wrapper:focus-within {
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-input-wrapper .icon {
  color: #94a3b8;
  margin-right: 12px;
}

.search-input {
  flex: 1;
  border: none;
  outline: none;
  font-size: 14px;
  color: #2d3748;
  background: transparent;
}

.search-input::placeholder {
  color: #94a3b8;
}

.clear-search {
  background: none;
  border: none;
  color: #94a3b8;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-search:hover {
  background: #f1f5f9;
  color: #64748b;
}

/* Search Results */
.search-results {
  margin-top: 16px;
}

.compact-menu-items {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 300px;
  overflow-y: auto;
  padding-right: 8px;
}

.compact-menu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.compact-menu-item:hover {
  background: #f8fafc;
  border-color: #667eea;
  transform: translateX(4px);
}

.compact-menu-item .item-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.compact-menu-item .item-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 14px;
}

.compact-menu-item .item-code {
  font-size: 12px;
  color: #667eea;
  font-weight: 500;
}

.compact-menu-item .item-price {
  font-weight: 600;
  color: #2d3748;
  margin-right: 12px;
}

.add-item-btn {
  background: #667eea;
  border: none;
  color: white;
  border-radius: 6px;
  padding: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-item-btn:hover {
  background: #5a67d8;
  transform: scale(1.1);
}

.more-results {
  text-align: center;
  padding: 12px;
  color: #64748b;
  font-size: 13px;
  font-style: italic;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px dashed #cbd5e1;
}

/* Search States */
.search-prompt,
.no-results {
  text-align: center;
  padding: 40px 20px;
  color: #64748b;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px dashed #cbd5e1;
}

.search-prompt .icon,
.no-results .icon {
  color: #94a3b8;
  margin-bottom: 12px;
}

.search-prompt p,
.no-results p {
  font-size: 16px;
  font-weight: 500;
  color: #475569;
  margin: 0 0 8px 0;
}

.search-prompt small,
.no-results small {
  font-size: 13px;
  color: #64748b;
}

/* Order Summary in Edit Modal */
.edit-order-modal .order-summary {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  margin-top: 8px;
}

.edit-order-modal .summary-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.edit-order-modal .summary-row.total {
  font-size: 16px;
  font-weight: 700;
  color: #2d3748;
  padding-top: 8px;
  border-top: 1px solid #e2e8f0;
  margin-top: 8px;
  margin-bottom: 0;
}

/* Modal Actions */
.edit-order-modal .modal-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid #e2e8f0;
  background: #f8fafc;
}

/* Responsive Design for Edit Order Modal */
@media (max-width: 768px) {
  .modal-content.edit-order-modal.compact {
    width: 95vw;
    max-height: 95vh;
  }

  .edit-order-content {
    padding: 16px;
    gap: 16px;
  }

  .order-items-list,
  .compact-menu-items {
    max-height: 200px;
  }

  .order-item-edit {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .item-controls {
    justify-content: space-between;
  }

  .compact-menu-item {
    padding: 12px;
  }

  .edit-order-modal .modal-actions {
    flex-direction: column;
    gap: 8px;
  }

  .search-prompt,
  .no-results {
    padding: 24px 16px;
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24px;
  border-bottom: 1px solid #e2e8f0;
}

.modal-header h2 {
  font-size: 20px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: #f7fafc;
  border-radius: 50%;
  cursor: pointer;
  font-size: 18px;
  color: #4a5568;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: #edf2f7;
}

.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 16px;
  padding: 24px;
}

.table-option {
  background: #f8fafc;
  border: 2px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.table-option:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: #667eea;
}

.table-number {
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
  margin-bottom: 8px;
}

.table-capacity {
  font-size: 14px;
  color: #667eea;
  margin-bottom: 4px;
}

.table-location {
  font-size: 12px;
  color: #718096;
}

.no-tables {
  text-align: center;
  padding: 40px 24px;
  color: #718096;
}

.no-tables p {
  margin-bottom: 16px;
}

.billing-details {
  padding: 24px;
}

.order-info {
  margin-bottom: 24px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 8px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.info-row:last-child {
  margin-bottom: 0;
}

.order-type-badge {
  background: #667eea;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.customer-info {
  margin-bottom: 24px;
}

.customer-info h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
}

.form-row {
  display: flex;
  gap: 16px;
}

.form-input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
}

.form-input:focus {
  outline: none;
  border-color: #667eea;
}

.order-items h3 {
  font-size: 16px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
}

.billing-summary {
  background: #f8fafc;
  padding: 20px;
  border-radius: 8px;
  margin: 24px 0;
}

.billing-actions {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
}

.billing-actions .btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.billing-actions .btn-secondary {
  background: #f7fafc;
  border: 2px solid #e2e8f0;
  color: #4a5568;
}

.billing-actions .btn-secondary:hover {
  background: #edf2f7;
}

.billing-actions .btn-primary {
  background: #667eea;
  border: 2px solid #667eea;
  color: white;
}

.billing-actions .btn-primary:hover {
  background: #5a67d8;
  border-color: #5a67d8;
}

/* Order Management Styles */
.order-management {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

.order-management-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

.order-management-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #2d3748;
  margin: 0;
}

.status-filter {
  display: flex;
  gap: 12px;
  align-items: center;
}

.status-select {
  padding: 8px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  min-width: 150px;
}

.refresh-btn {
  padding: 8px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.refresh-btn:hover {
  background: #5a67d8;
}

.order-management-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  color: #718096;
}

.order-management-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #667eea;
  margin-bottom: 20px;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.empty-orders {
  grid-column: 1 / -1;
  text-align: center;
  padding: 60px 20px;
  color: #718096;
}

.empty-orders .empty-icon {
  font-size: 48px;
  display: block;
  margin-bottom: 16px;
}

.empty-orders h3 {
  font-size: 20px;
  color: #2d3748;
  margin-bottom: 8px;
}

.order-card {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  border: 2px solid #e2e8f0;
  transition: all 0.3s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.order-card.status-pending {
  border-left: 4px solid #f59e0b;
}

.order-card.status-confirmed {
  border-left: 4px solid #3b82f6;
}

.order-card.status-preparing {
  border-left: 4px solid #8b5cf6;
}

.order-card.status-ready {
  border-left: 4px solid #10b981;
}

.order-card.status-served {
  border-left: 4px solid #22c55e;
}

.order-card.status-completed {
  border-left: 4px solid #059669;
}

.order-card.status-cancelled {
  border-left: 4px solid #ef4444;
  opacity: 0.7;
}

.order-card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.order-number {
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
}

.order-status {
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.order-card-body {
  margin-bottom: 16px;
}

.order-info {
  margin-bottom: 16px;
}

.info-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 4px;
}

.info-label {
  font-weight: 500;
  color: #4a5568;
}

.info-value {
  color: #2d3748;
  font-weight: 600;
}

.order-items-summary {
  background: #f8fafc;
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
}

.item-summary {
  color: #4a5568;
  margin-bottom: 2px;
}

.more-items {
  color: #718096;
  font-style: italic;
  margin-top: 4px;
}

.order-card-actions {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  border-top: 1px solid #e2e8f0;
  padding-top: 16px;
}

.order-card-actions button {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  flex: 1;
  min-width: 80px;
}

.kot-btn {
  background: #fef3c7;
  color: #92400e;
}

.kot-btn:hover {
  background: #fde68a;
}

.status-action-btn {
  color: white;
}

.status-action-btn:hover {
  opacity: 0.9;
  transform: translateY(-1px);
}

.details-btn {
  background: #e0e7ff;
  color: #3730a3;
}

.details-btn:hover {
  background: #c7d2fe;
}

/* Order Details Modal */
.order-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.order-details-content {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.order-summary {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.summary-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.info-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
}

.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.info-item .label {
  font-weight: 500;
  color: #4a5568;
}

.info-item .value {
  font-weight: 600;
  color: #2d3748;
}

.status-badge {
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.items-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: #f8fafc;
  border-radius: 8px;
}

.item-row .item-info {
  flex: 1;
}

.item-row .item-name {
  font-weight: 600;
  color: #2d3748;
  display: block;
  margin-bottom: 4px;
}

.item-row .item-price {
  font-size: 12px;
  color: #718096;
}

.item-quantity {
  font-weight: 500;
  color: #4a5568;
  margin: 0 16px;
}

.item-subtotal {
  font-weight: 700;
  color: #667eea;
  min-width: 80px;
  text-align: right;
}

.payment-summary {
  background: #f8fafc;
  padding: 16px;
  border-radius: 8px;
}

.payment-summary .summary-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.payment-summary .summary-row.total {
  font-size: 18px;
  font-weight: 700;
  border-top: 1px solid #e2e8f0;
  padding-top: 8px;
  margin-top: 8px;
}

.payment-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
}

.payment-status.pending {
  background: #fef3c7;
  color: #92400e;
}

.payment-status.paid {
  background: #d1fae5;
  color: #065f46;
}

.payment-status.refunded {
  background: #fee2e2;
  color: #991b1b;
}

.billing-btn {
  background: #10b981;
  color: white;
}

.billing-btn:hover {
  background: #059669;
}

/* Billing System Styles */
.billing-modal-content {
  width: 900px;
  max-width: 95vw;
}

.billing-content {
  padding: 24px;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.order-summary-section {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.order-summary-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.order-details {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.order-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.items-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #e2e8f0;
}

.item-row:last-child {
  border-bottom: none;
}

.item-name {
  flex: 1;
  font-weight: 500;
  color: #2d3748;
}

.item-qty {
  margin: 0 16px;
  color: #4a5568;
  font-weight: 500;
}

.item-total {
  font-weight: 600;
  color: #667eea;
  min-width: 80px;
  text-align: right;
}

.billing-totals {
  border-top: 2px solid #e2e8f0;
  padding-top: 16px;
  margin-top: 16px;
}

.total-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;
}

.total-row.grand-total {
  font-size: 18px;
  font-weight: 700;
  color: #2d3748;
  border-top: 1px solid #e2e8f0;
  padding-top: 8px;
  margin-top: 8px;
}

.payment-section {
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
}

.payment-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 20px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
}

.payment-method-selector {
  margin-bottom: 20px;
}

.payment-method-selector label {
  display: block;
  font-weight: 600;
  color: #4a5568;
  margin-bottom: 12px;
}

.payment-methods {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
}

.payment-method-btn {
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  background: white;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
}

.payment-method-btn:hover {
  border-color: #667eea;
  background: #f0f4ff;
}

.payment-method-btn.active {
  border-color: #667eea;
  background: #667eea;
  color: white;
}

.payment-inputs {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #4a5568;
}

.amount-input {
  font-size: 18px;
  font-weight: 600;
  padding: 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
}

.amount-input:focus {
  outline: none;
  border-color: #667eea;
}

.change-display {
  background: #f0fdf4;
  border: 1px solid #bbf7d0;
  border-radius: 8px;
  padding: 16px;
  text-align: center;
}

.change-amount {
  font-size: 18px;
  color: #166534;
}

.payment-validation {
  margin-top: 12px;
}

.validation-error {
  background: #fef2f2;
  border: 1px solid #fecaca;
  color: #991b1b;
  padding: 12px;
  border-radius: 8px;
  font-size: 14px;
}

.billing-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding-top: 20px;
  border-top: 1px solid #e2e8f0;
}

.billing-actions .btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 120px;
}

.billing-actions .btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.billing-actions .btn-secondary {
  background: #f7fafc;
  border: 2px solid #e2e8f0;
  color: #4a5568;
}

.billing-actions .btn-secondary:hover:not(:disabled) {
  background: #edf2f7;
}

.billing-actions .btn-primary {
  background: #667eea;
  border: 2px solid #667eea;
  color: white;
}

.billing-actions .btn-primary:hover:not(:disabled) {
  background: #5a67d8;
  border-color: #5a67d8;
}

/* Receipt Modal Styles */
.receipt-modal {
  width: 600px;
}

.receipt-content {
  padding: 24px;
}

.receipt-preview {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  max-height: 400px;
  overflow-y: auto;
}

.receipt-preview pre {
  font-family: "Courier New", monospace;
  font-size: 12px;
  line-height: 1.4;
  margin: 0;
  white-space: pre-wrap;
}

.receipt-actions {
  display: flex;
  justify-content: center;
  gap: 16px;
}

.receipt-actions .btn {
  padding: 12px 24px;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

/* Enhanced Receipt Preview Styles */
.receipt-preview-section {
  background: #f8fafc;
  padding: 20px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
  margin-bottom: 24px;
}

.receipt-preview-section h3 {
  font-size: 18px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e2e8f0;
  text-align: center;
}

.live-receipt-preview {
  background: white;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.receipt-preview-container {
  max-width: 300px;
  margin: 0 auto;
  font-family: "Courier New", monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #000;
}

.receipt-preview-container .receipt {
  max-width: none;
}

.receipt-preview-container .receipt-header {
  text-align: center;
  border-bottom: 2px solid #000;
  padding-bottom: 10px;
  margin-bottom: 10px;
}

.receipt-preview-container .receipt-logo {
  max-width: 60px;
  height: auto;
  margin-bottom: 8px;
}

.receipt-preview-container .restaurant-name {
  font-size: 14px;
  font-weight: bold;
  margin: 5px 0;
  text-transform: uppercase;
}

.receipt-preview-container .address {
  font-size: 10px;
  margin: 2px 0;
}

.receipt-preview-container .contact-info p {
  font-size: 9px;
  margin: 1px 0;
}

.receipt-preview-container .receipt-divider {
  text-align: center;
  border-bottom: 1px solid #000;
  padding: 5px 0;
  margin: 8px 0;
}

.receipt-preview-container .receipt-divider h2 {
  font-size: 12px;
  font-weight: bold;
  margin: 0;
}

.receipt-preview-container .detail-row {
  display: flex;
  justify-content: space-between;
  margin: 2px 0;
  font-size: 10px;
}

.receipt-preview-container .items-section h3 {
  font-size: 11px;
  font-weight: bold;
  text-align: center;
  border-bottom: 1px solid #000;
  padding-bottom: 3px;
  margin-bottom: 8px;
}

.receipt-preview-container .item {
  margin: 6px 0;
  border-bottom: 1px dotted #ccc;
  padding-bottom: 3px;
}

.receipt-preview-container .item-name {
  font-weight: bold;
  font-size: 10px;
}

.receipt-preview-container .item-line {
  display: flex;
  justify-content: space-between;
  font-size: 9px;
  margin: 1px 0;
}

.receipt-preview-container .total-row {
  display: flex;
  justify-content: space-between;
  margin: 2px 0;
  font-size: 10px;
}

.receipt-preview-container .grand-total {
  font-weight: bold;
  font-size: 11px;
  border-top: 1px solid #000;
  padding-top: 3px;
  margin-top: 5px;
}

.receipt-preview-container .payment-section h3 {
  font-size: 11px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 6px;
}

.receipt-preview-container .payment-row {
  display: flex;
  justify-content: space-between;
  margin: 1px 0;
  font-size: 9px;
}

.receipt-preview-container .receipt-footer {
  border-top: 1px solid #000;
  padding-top: 8px;
  margin-top: 10px;
  text-align: center;
}

.receipt-preview-container .receipt-footer p {
  font-size: 9px;
  margin: 2px 0;
}

.receipt-html-preview {
  max-width: 350px;
  margin: 0 auto;
  background: white;
  padding: 20px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.receipt-html-preview .receipt {
  max-width: none;
}

/* Improved Amount Input */
.amount-input {
  font-size: 20px !important;
  font-weight: 700 !important;
  text-align: center;
  padding: 16px !important;
  border: 3px solid #e2e8f0 !important;
  border-radius: 12px !important;
  background: #f8fafc;
}

.amount-input:focus {
  outline: none !important;
  border-color: #667eea !important;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.change-display {
  background: linear-gradient(135deg, #f0fdf4 0%, #dcfce7 100%);
  border: 2px solid #bbf7d0;
  border-radius: 12px;
  padding: 20px;
  text-align: center;
  margin: 16px 0;
}

.change-amount {
  font-size: 20px;
  font-weight: 700;
  color: #166534;
}

/* Payment Method Buttons */
.payment-methods {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 12px;
  margin-bottom: 20px;
}

.payment-method-btn {
  padding: 16px 20px;
  border: 3px solid #e2e8f0;
  background: white;
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  font-weight: 600;
  text-align: center;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.payment-method-btn:hover {
  border-color: #667eea;
  background: #f0f4ff;
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.2);
}

.payment-method-btn.active {
  border-color: #667eea;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3);
}

/* Table Orders Modal Styles */
.table-orders-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
}

.table-orders-content {
  padding: 24px;
  max-height: 70vh;
  overflow-y: auto;
}

.no-orders {
  text-align: center;
  padding: 40px 20px;
  color: #718096;
}

.orders-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.order-summary-card {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 16px;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.order-number {
  font-size: 16px;
  font-weight: 700;
  color: #2d3748;
}

.order-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.order-status.status-pending {
  background: #fef3c7;
  color: #92400e;
}

.order-status.status-confirmed {
  background: #dbeafe;
  color: #1e40af;
}

.order-status.status-preparing {
  background: #f3e8ff;
  color: #7c3aed;
}

.order-status.status-ready {
  background: #d1fae5;
  color: #065f46;
}

.order-status.status-served {
  background: #dcfce7;
  color: #166534;
}

.order-items {
  margin-bottom: 12px;
}

.order-item-summary {
  display: flex;
  justify-content: space-between;
  padding: 4px 0;
  font-size: 14px;
  border-bottom: 1px dotted #e2e8f0;
}

.order-item-summary:last-child {
  border-bottom: none;
}

.order-total {
  text-align: right;
  margin-bottom: 12px;
  font-size: 16px;
  color: #2d3748;
}

.order-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid #e2e8f0;
  padding-top: 12px;
}

.bill-btn {
  background: #10b981;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.bill-btn:hover {
  background: #059669;
  transform: translateY(-1px);
}

.payment-status {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 10px;
  font-weight: 600;
  text-transform: uppercase;
}

.payment-status.pending {
  background: #fef3c7;
  color: #92400e;
}

.payment-status.paid {
  background: #d1fae5;
  color: #065f46;
}

.orders-btn {
  background: #3b82f6;
  color: white;
}

.orders-btn:hover {
  background: #2563eb;
}

/* Order Management Controls */
.order-management-header {
  padding: 20px;
  background: white;
  border-bottom: 1px solid #dee2e6;
}

.order-management-header h2 {
  margin: 0 0 20px 0;
  color: #2d3748;
  font-size: 24px;
  font-weight: 600;
}

.order-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.search-section {
  flex: 1;
  min-width: 300px;
}

.search-input {
  width: 100%;
  padding: 10px 16px;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.search-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.filter-section {
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
}

.status-select,
.page-size-select {
  padding: 8px 12px;
  border: 1px solid #dee2e6;
  border-radius: 6px;
  background: white;
  font-size: 14px;
  color: #495057;
  cursor: pointer;
  transition: border-color 0.2s;
  min-width: 120px;
}

.status-select:focus,
.page-size-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.refresh-btn {
  padding: 8px 16px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.refresh-btn:hover {
  background: #5a67d8;
}

/* Order Management List View Styles */
.orders-list-container {
  padding: 0;
}

.orders-table {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.orders-table-header {
  display: grid;
  grid-template-columns: 150px 120px 120px 120px 120px 320px;
  background: #f8f9fa;
  border-bottom: 2px solid #dee2e6;
  font-weight: bold;
  padding: 15px 20px;
  align-items: center;
}

.header-cell {
  color: #495057;
  font-size: 14px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  display: flex;
  align-items: center;
}

.order-row {
  display: grid;
  grid-template-columns: 150px 120px 120px 120px 120px 320px;
  padding: 15px 20px;
  border-bottom: 1px solid #dee2e6;
  transition: background-color 0.2s;
  align-items: center;
  min-height: 60px;
}

.order-row:hover {
  background-color: #f8f9fa;
}

.order-cell {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #495057;
  padding: 4px 0;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: bold;
  text-transform: uppercase;
  white-space: nowrap;
}

.actions-cell {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: nowrap;
  position: relative;
}

.btn-sm {
  padding: 6px 12px;
  font-size: 11px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 600;
  white-space: nowrap;
  min-width: 80px;
  text-align: center;
}

.btn-primary {
  background-color: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background-color: #2563eb;
}

.btn-success {
  background-color: #10b981;
  color: white;
}

.btn-success:hover {
  background-color: #059669;
}

.btn-success:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #6b7280;
  color: white;
}

.btn-secondary:hover {
  background-color: #4b5563;
}

.btn-secondary:disabled {
  background-color: #9ca3af;
  cursor: not-allowed;
}

.btn-primary {
  background-color: #007bff;
  color: white;
}

.btn-primary:hover {
  background-color: #0056b3 !important;
  color: white !important;
  opacity: 1 !important;
}

.btn-success {
  background-color: #28a745;
  color: white;
}

.btn-success:hover {
  background-color: #1e7e34 !important;
  color: white !important;
  opacity: 1 !important;
}

.btn-success:disabled {
  background-color: #6c757d;
  cursor: not-allowed;
}

.btn-secondary {
  background-color: #6c757d;
  color: white;
}

.btn-secondary:hover {
  background-color: #545b62 !important;
  color: white !important;
  opacity: 1 !important;
}

/* Fix for order management buttons specifically */
.order-management .btn-primary,
.order-management .btn-success,
.order-management .btn-secondary {
  font-weight: 600;
  text-shadow: none;
  border: none;
}

.order-management .btn-primary:hover,
.order-management .btn-success:hover,
.order-management .btn-secondary:hover {
  color: white !important;
  opacity: 1 !important;
  text-shadow: none !important;
}

.dropdown-container {
  position: relative;
}

.dropdown-toggle {
  padding: 6px 8px;
  font-size: 16px;
  font-weight: bold;
}

.dropdown-container {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #dee2e6;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1001;
  min-width: 140px;
  margin-top: 4px;
  overflow: hidden;
}

.dropdown-item {
  display: block;
  width: 100%;
  padding: 10px 14px;
  border: none;
  background: none;
  text-align: left;
  cursor: pointer;
  font-size: 13px;
  color: #495057;
  transition: background-color 0.2s;
  white-space: nowrap;
}

.dropdown-item:hover {
  background-color: #f8f9fa;
}

.dropdown-item:disabled {
  color: #6c757d;
  cursor: not-allowed;
  opacity: 0.6;
}

.dropdown-item:disabled:hover {
  background-color: transparent;
}

.dropdown-toggle {
  background: #6c757d !important;
  color: white !important;
  border: none;
  padding: 6px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: background-color 0.2s;
}

.dropdown-toggle:hover {
  background: #5a6268 !important;
}

/* Order Management Pagination Styles */
.pagination-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: white;
  border-top: 1px solid #dee2e6;
  border-radius: 0 0 8px 8px;
  margin-top: 0;
}

.pagination-info {
  color: #6c757d;
  font-size: 14px;
  font-weight: 500;
}

.pagination-buttons {
  display: flex;
  align-items: center;
  gap: 8px;
}

.pagination-btn {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
  min-width: 80px;
}

.pagination-btn:hover:not(:disabled) {
  background: #f8f9fa;
  border-color: #adb5bd;
}

.pagination-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: #f8f9fa;
}

.page-info {
  padding: 8px 16px;
  color: #495057;
  font-weight: 600;
  font-size: 14px;
  white-space: nowrap;
}

/* Order Type Badge */
.order-type-badge {
  background: #e9ecef;
  color: #495057;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  white-space: nowrap;
}

/* Responsive Design for Order Management */
@media (max-width: 1200px) {
  .orders-table-header,
  .order-row {
    grid-template-columns: 130px 100px 100px 100px 100px 280px;
  }

  .btn-sm {
    padding: 5px 10px;
    font-size: 10px;
    min-width: 70px;
  }

  .actions-cell {
    gap: 6px;
  }
}

@media (max-width: 992px) {
  .orders-table-header,
  .order-row {
    grid-template-columns: 120px 90px 90px 90px 90px 240px;
  }

  .btn-sm {
    padding: 4px 8px;
    font-size: 9px;
    min-width: 60px;
  }

  .actions-cell {
    gap: 4px;
    flex-wrap: wrap;
  }

  .pagination-controls {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .pagination-buttons {
    justify-content: center;
  }
}

@media (max-width: 768px) {
  .orders-table-header,
  .order-row {
    grid-template-columns: 1fr;
    grid-template-rows: auto;
    gap: 8px;
  }

  .order-row {
    padding: 16px;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    margin-bottom: 12px;
    background: white;
  }

  .order-cell {
    justify-content: space-between;
    padding: 4px 0;
    border-bottom: 1px solid #f8f9fa;
  }

  .order-cell:last-child {
    border-bottom: none;
  }

  .order-cell::before {
    content: attr(data-label);
    font-weight: 600;
    color: #6c757d;
    font-size: 12px;
    text-transform: uppercase;
  }

  .actions-cell {
    justify-content: center;
    flex-wrap: wrap;
    gap: 8px;
    margin-top: 8px;
  }

  .actions-cell::before {
    display: none;
  }

  .order-controls {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .search-section {
    min-width: auto;
  }

  .filter-section {
    justify-content: center;
    flex-wrap: wrap;
  }

  .status-select,
  .page-size-select {
    min-width: 100px;
    flex: 1;
  }
}

/* Modern User Profile Styles */
.modern-user-profile {
  max-width: 100%;
  overflow-x: hidden;
  padding: 0;
}

/* Modern Toast */
.modern-toast {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1000;
  background: white;
  border-radius: 12px;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  padding: 16px 20px;
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 300px;
  animation: slideInRight 0.3s ease-out;
}

.modern-toast-success {
  border-left: 4px solid #10b981;
}

.modern-toast-error {
  border-left: 4px solid #ef4444;
}

.modern-toast-info {
  border-left: 4px solid #3b82f6;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Modern Profile Card */
.modern-profile-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 20px;
  padding: 32px;
  margin-bottom: 32px;
  color: white;
  box-shadow: 0 20px 40px rgba(102, 126, 234, 0.3);
}

.profile-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32px;
}

.profile-avatar-section {
  display: flex;
  align-items: center;
  gap: 24px;
}

.modern-profile-avatar {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  border: 4px solid rgba(255, 255, 255, 0.3);
  background: rgba(255, 255, 255, 0.1);
}

.avatar-edit-overlay {
  position: absolute;
  bottom: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.7);
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.profile-name {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.profile-role {
  font-size: 16px;
  opacity: 0.9;
  margin: 0 0 16px 0;
}

.profile-badges {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.profile-id-badge {
  background: rgba(255, 255, 255, 0.2);
  padding: 6px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 500;
}

/* Modern Buttons */
.modern-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-weight: 600;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;
}

.modern-btn-primary {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  backdrop-filter: blur(10px);
}

.modern-btn-primary:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.modern-btn-secondary {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.modern-btn-secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

.edit-actions {
  display: flex;
  gap: 12px;
}

/* Profile Stats */
.profile-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 16px;
  background: rgba(255, 255, 255, 0.1);
  padding: 20px;
  border-radius: 16px;
  backdrop-filter: blur(10px);
}

.stat-item.trial-warning {
  background: rgba(239, 68, 68, 0.2);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

.stat-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-content {
  flex: 1;
}

.stat-label {
  display: block;
  font-size: 12px;
  opacity: 0.8;
  margin-bottom: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.stat-value {
  display: block;
  font-size: 16px;
  font-weight: 600;
}

/* Modern Sections */
.modern-profile-sections {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

.modern-section-card {
  background: white;
  border-radius: 20px;
  padding: 32px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 32px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.section-icon {
  font-size: 24px;
  width: 48px;
  height: 48px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.section-title {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

/* Modern Form Styles */
.modern-form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.modern-form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.modern-form-group.full-width {
  grid-column: 1 / -1;
}

.modern-form-label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.label-icon {
  font-size: 16px;
}

.modern-form-input {
  padding: 16px;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: #fafafa;
}

.modern-form-input:focus {
  outline: none;
  border-color: #667eea;
  background: white;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.modern-form-input.error {
  border-color: #ef4444;
  background: #fef2f2;
}

.modern-form-display {
  padding: 16px;
  background: #f8fafc;
  border: 2px solid #e5e7eb;
  border-radius: 12px;
  color: #374151;
  min-height: 24px;
  display: flex;
  align-items: center;
}

.modern-form-display.readonly {
  background: #f1f5f9;
  color: #64748b;
  font-family: "Courier New", monospace;
  font-size: 14px;
}

.modern-error-message {
  color: #ef4444;
  font-size: 12px;
  font-weight: 500;
  margin-top: 4px;
}

.image-upload-section {
  margin-bottom: 24px;
  padding: 24px;
  background: #f8fafc;
  border-radius: 16px;
  border: 2px dashed #d1d5db;
}

@media (max-width: 768px) {
  .modern-profile-card {
    padding: 24px;
  }

  .profile-card-header {
    flex-direction: column;
    gap: 24px;
  }

  .profile-avatar-section {
    flex-direction: column;
    text-align: center;
  }

  .profile-stats {
    grid-template-columns: 1fr;
  }

  .modern-form-grid {
    grid-template-columns: 1fr;
  }
}

/* POS Window Redirect Message */
.pos-redirect-message {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 40px;
}

.pos-redirect-content {
  text-align: center;
  background: white;
  padding: 48px;
  border-radius: 16px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
  max-width: 500px;
  border: 1px solid #e2e8f0;
}

.pos-redirect-content svg {
  color: #3b82f6;
  margin-bottom: 24px;
}

.pos-redirect-content h3 {
  color: #1e293b;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 16px;
}

.pos-redirect-content p {
  color: #64748b;
  font-size: 16px;
  line-height: 1.6;
  margin-bottom: 32px;
}

.pos-redirect-content .btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  font-size: 16px;
  font-weight: 500;
}

/* POS Window Loading Styles */
.pos-loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f8fafc;
  gap: 16px;
}

.pos-loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.pos-loading-text {
  color: #64748b;
  font-size: 16px;
  font-weight: 500;
}

/* Dedicated POS Window Styles */
.dedicated-pos-window {
  height: 100vh;
  background: #f8fafc;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.pos-content-area {
  flex: 1;
  overflow: hidden;
}

/* POS Window Header */
.pos-window-header {
  background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
  color: white;
  padding: 8px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  -webkit-app-region: drag;
  min-height: 48px;
  border-bottom: 1px solid #475569;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.pos-window-title {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.pos-title-text {
  font-size: 14px;
  font-weight: 600;
  color: white;
  display: flex;
  align-items: center;
  gap: 8px;
}

.active-order-indicator {
  color: #10b981;
  font-size: 12px;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pos-subtitle {
  font-size: 11px;
  font-weight: 400;
  color: #94a3b8;
  transition: color 0.3s ease;
}

.pos-window-controls {
  display: flex;
  gap: 4px;
  -webkit-app-region: no-drag;
}

.pos-control-btn {
  width: 36px;
  height: 28px;
  border: none;
  background: transparent;
  color: #94a3b8;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  -webkit-app-region: no-drag;
}

.pos-control-btn:hover {
  background: rgba(255, 255, 255, 0.1);
  color: white;
  transform: translateY(-1px);
}

.pos-control-btn.minimize:hover {
  background: rgba(59, 130, 246, 0.2);
  color: #60a5fa;
}

.pos-control-btn.maximize:hover {
  background: rgba(34, 197, 94, 0.2);
  color: #4ade80;
}

.pos-control-btn.close:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #f87171;
}

.pos-control-btn:active {
  transform: translateY(0);
}

/* POS Error Styles */
.pos-error-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #f8fafc;
  padding: 20px;
}

.pos-error-content {
  text-align: center;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  max-width: 400px;
}

.pos-error-content h2 {
  color: #dc2626;
  margin-bottom: 16px;
  font-size: 24px;
}

.pos-error-content p {
  color: #64748b;
  margin-bottom: 24px;
  line-height: 1.6;
}

.pos-error-close-btn {
  background: #dc2626;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: background 0.2s ease;
}

.pos-error-close-btn:hover {
  background: #b91c1c;
}

/* Window Controls */
.window-controls {
  display: flex;
  align-items: center;
  gap: 0;
  margin-left: 16px;
  -webkit-app-region: no-drag;
}

.window-control-btn {
  width: 46px;
  height: 32px;
  border: none;
  background: transparent;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  -webkit-app-region: no-drag;
}

.window-control-btn:hover {
  background: rgba(0, 0, 0, 0.05);
  color: #374151;
}

.window-control-btn.close-btn:hover {
  background: #ef4444;
  color: white;
}

.window-control-btn svg {
  width: 12px;
  height: 12px;
}

/* Make the topbar draggable */
.dashboard-topbar {
  -webkit-app-region: drag;
}

.dashboard-topbar .topbar-right > * {
  -webkit-app-region: no-drag;
}

/* Simplified Header Styles */
.restaurant-name-header {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  max-width: 300px;
}

.current-time {
  font-size: 14px;
  color: #64748b;
  font-weight: 500;
  padding: 8px 16px;
  background: #f8fafc;
  border-radius: 8px;
  border: 1px solid #e2e8f0;
}

.new-order-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.new-order-btn:hover {
  background: #5a67d8;
  transform: translateY(-1px);
}

/* Form help text */
.form-help {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
  display: block;
}

/* Analytics Page Styles */
.analytics-page {
  padding: 24px;
  background: #f8fafc;
  min-height: 100vh;
}

.analytics-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.analytics-header h1 {
  font-size: 32px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.period-selector {
  display: flex;
  background: white;
  border-radius: 12px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.period-btn {
  padding: 8px 16px;
  border: none;
  background: transparent;
  color: #64748b;
  font-weight: 500;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.period-btn.active {
  background: #667eea;
  color: white;
  box-shadow: 0 2px 4px rgba(102, 126, 234, 0.3);
}

.period-btn:hover:not(.active) {
  background: #f1f5f9;
  color: #475569;
}

/* Overview Cards */
.analytics-overview {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 32px;
}

.overview-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 20px;
  transition: all 0.3s ease;
}

.overview-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.card-icon {
  width: 64px;
  height: 64px;
  border-radius: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.card-icon.tables {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.card-icon.menu {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.card-icon.orders {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.card-icon.revenue {
  background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.card-content h3 {
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 4px 0;
}

.card-content p {
  font-size: 16px;
  color: #64748b;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.card-subtitle {
  font-size: 14px;
  color: #94a3b8;
}

/* Analytics Details */
.analytics-details {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.analytics-section {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
}

.analytics-section h2 {
  font-size: 20px;
  font-weight: 600;
  color: #1a202c;
  margin: 0 0 20px 0;
}

/* Performance Grid */
.performance-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 16px;
}

.performance-card {
  text-align: center;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.performance-card h4 {
  font-size: 14px;
  color: #64748b;
  margin: 0 0 8px 0;
  font-weight: 500;
}

.performance-value {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
}

/* Status Breakdown */
.status-breakdown {
  display: flex;
  justify-content: space-around;
  gap: 16px;
}

.status-item {
  text-align: center;
  padding: 20px;
  border-radius: 12px;
  flex: 1;
}

.status-item.completed {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
}

.status-item.active {
  background: linear-gradient(135deg, #cce5ff 0%, #b3d9ff 100%);
  color: #004085;
}

.status-item.cancelled {
  background: linear-gradient(135deg, #f8d7da 0%, #f1b0b7 100%);
  color: #721c24;
}

.status-count {
  font-size: 32px;
  font-weight: 700;
  margin-bottom: 4px;
}

.status-label {
  font-size: 14px;
  font-weight: 500;
}

/* Popular Items */
.popular-items {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.popular-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f8fafc;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.item-rank {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #667eea;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 14px;
  flex-shrink: 0;
}

.item-details {
  flex: 1;
}

.item-name {
  font-size: 16px;
  font-weight: 600;
  color: #1a202c;
  margin-bottom: 4px;
}

.item-stats {
  font-size: 14px;
  color: #64748b;
}

/* Busy Hours */
.busy-hours {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.busy-hour {
  display: flex;
  align-items: center;
  gap: 16px;
}

.hour-time {
  width: 60px;
  font-size: 14px;
  font-weight: 500;
  color: #64748b;
  flex-shrink: 0;
}

.hour-bar {
  flex: 1;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
}

.hour-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
  border-radius: 4px;
  transition: width 0.3s ease;
}

.hour-count {
  width: 40px;
  text-align: right;
  font-size: 14px;
  font-weight: 600;
  color: #1a202c;
  flex-shrink: 0;
}

/* Loading and Error States */
.analytics-loading,
.analytics-error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  text-align: center;
}

.analytics-loading p,
.analytics-error p {
  font-size: 18px;
  color: #64748b;
  margin: 16px 0;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (max-width: 768px) {
  .analytics-page {
    padding: 16px;
  }

  .analytics-header {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }

  .analytics-overview {
    grid-template-columns: 1fr;
  }

  .analytics-details {
    grid-template-columns: 1fr;
  }

  .overview-card {
    flex-direction: column;
    text-align: center;
  }

  .status-breakdown {
    flex-direction: column;
  }

  .performance-grid {
    grid-template-columns: 1fr;
  }
}

/* Feature unavailable styles */
.feature-unavailable {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 60vh;
  text-align: center;
  padding: 40px;
}

.feature-unavailable h3 {
  font-size: 24px;
  color: #374151;
  margin-bottom: 16px;
}

.feature-unavailable p {
  font-size: 16px;
  color: #6b7280;
  margin-bottom: 8px;
  max-width: 500px;
}

.feature-unavailable .btn {
  margin-top: 24px;
}

/* Settings Tab Styles */
.settings-tabs-nav {
  display: flex;
  gap: 8px;
  margin-bottom: 32px;
  border-bottom: 2px solid #f1f5f9;
  padding-bottom: 16px;
}

.settings-tab-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 20px;
  border: none;
  background: transparent;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  color: #64748b;
  font-weight: 500;
}

.settings-tab-item:hover {
  background: #f8fafc;
  color: #374151;
}

.settings-tab-item.active {
  background: #667eea;
  color: white;
}

.tab-item-icon {
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-item-label {
  font-size: 14px;
  font-weight: 600;
}

.settings-content-header {
  margin-bottom: 32px;
}

.content-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 28px;
  font-weight: 700;
  color: #1a202c;
  margin-bottom: 8px;
}

.content-description {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

/* Settings Section Styles */
.settings-section {
  background: white;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  border: 1px solid #f1f5f9;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.section-title {
  font-size: 20px;
  font-weight: 700;
  color: #1a202c;
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 12px;
}

.edit-actions {
  display: flex;
  gap: 12px;
}

/* Form Styles for Settings */
.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group.full-width {
  grid-column: 1 / -1;
}

.form-label {
  font-weight: 600;
  color: #374151;
  font-size: 14px;
}

.form-input,
.form-select {
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;
  background: white;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.form-input.error {
  border-color: #ef4444;
  background: #fef2f2;
}

.form-display {
  padding: 12px 16px;
  background: #f8fafc;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  color: #374151;
  min-height: 20px;
  display: flex;
  align-items: center;
}

.form-display.readonly {
  background: #f1f5f9;
  color: #64748b;
  font-family: "Courier New", monospace;
  font-size: 12px;
}

.error-message {
  color: #ef4444;
  font-size: 12px;
  font-weight: 500;
  margin-top: 4px;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-trial {
  background: #fef3c7;
  color: #92400e;
}

.status-active {
  background: #d1fae5;
  color: #065f46;
}

.status-expired {
  background: #fee2e2;
  color: #991b1b;
}

.status-cancelled {
  background: #f3f4f6;
  color: #374151;
}

/* Subscription Styles */
.subscription-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.subscription-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #f1f5f9;
}

.subscription-plan {
  font-size: 24px;
  font-weight: 700;
  margin: 0 0 8px 0;
  color: #1a202c;
}

.subscription-price {
  font-size: 32px;
  font-weight: 700;
  color: #667eea;
}

.subscription-details {
  display: grid;
  gap: 12px;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f1f5f9;
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-label {
  font-weight: 500;
  color: #64748b;
}

.detail-value {
  font-weight: 600;
  color: #374151;
}

.trial-warning {
  background: #fef2f2;
  border: 1px solid #fecaca;
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
}

.trial-warning h4 {
  margin: 0 0 8px 0;
  color: #dc2626;
}

.trial-warning p {
  margin: 0;
  color: #7f1d1d;
}

/* Plans Grid */
.plans-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.plan-card {
  background: white;
  border: 2px solid #e5e7eb;
  border-radius: 16px;
  padding: 32px;
  position: relative;
  transition: all 0.3s ease;
}

.plan-card:hover {
  border-color: #667eea;
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
}

.plan-card.recommended {
  border-color: #667eea;
  box-shadow: 0 10px 25px rgba(102, 126, 234, 0.15);
}

.plan-badge {
  position: absolute;
  top: -12px;
  left: 50%;
  transform: translateX(-50%);
  background: #667eea;
  color: white;
  padding: 6px 16px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
}

.plan-header {
  text-align: center;
  margin-bottom: 24px;
}

.plan-name {
  font-size: 24px;
  font-weight: 700;
  color: #1a202c;
  margin: 0 0 16px 0;
}

.plan-price {
  display: flex;
  align-items: baseline;
  justify-content: center;
  gap: 4px;
}

.price {
  font-size: 48px;
  font-weight: 700;
  color: #667eea;
}

.period {
  font-size: 16px;
  color: #64748b;
}

.plan-features ul {
  list-style: none;
  padding: 0;
  margin: 0;
}

.plan-features li {
  padding: 8px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.plan-features li:before {
  content: "✓";
  color: #10b981;
  font-weight: bold;
}

.plan-actions {
  margin-top: 24px;
}

.plan-actions .btn {
  width: 100%;
}

/* Billing History */
.billing-history {
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
}

.history-item {
  padding: 16px 0;
  border-bottom: 1px solid #e5e7eb;
}

.history-item:last-child {
  border-bottom: none;
}

.history-date {
  font-weight: 600;
  color: #374151;
  margin-bottom: 4px;
}

.history-description {
  color: #64748b;
  font-size: 14px;
}

/* Support Cards */
.support-info,
.backup-info {
  background: #f8fafc;
  border-radius: 12px;
  padding: 24px;
}

.support-card,
.info-card {
  background: white;
  border-radius: 12px;
  padding: 24px;
  border: 1px solid #e5e7eb;
}

.support-card h4,
.info-card h4 {
  margin: 0 0 12px 0;
  color: #1a202c;
  font-size: 18px;
  font-weight: 700;
}

.support-card p,
.info-card p {
  margin: 0 0 20px 0;
  color: #64748b;
  line-height: 1.6;
}

.support-actions,
.backup-actions {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.website-link {
  color: #667eea;
  text-decoration: none;
}

.website-link:hover {
  text-decoration: underline;
}

.text-warning {
  color: #f59e0b !important;
  font-weight: 600;
}

@media (max-width: 768px) {
  .settings-tabs-nav {
    flex-direction: column;
    gap: 4px;
  }

  .form-grid {
    grid-template-columns: 1fr;
  }

  .plans-grid {
    grid-template-columns: 1fr;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .support-actions,
  .backup-actions {
    flex-direction: column;
  }
}

@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }
}

.form-group {
  display: flex;
  flex-direction: column;
  max-width: 100%;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  max-width: 100%;
  box-sizing: border-box;
}

/* Settings Content Body Fix */
.settings-content-body {
  max-width: 100%;
  overflow-x: hidden;
  padding: 20px;
}

/* Error Message Styling */
.error-message {
  background: #fed7d7;
  color: #c53030;
  padding: 16px;
  border-radius: 8px;
  border: 1px solid #feb2b2;
  text-align: center;
  font-weight: 500;
}

/* Modern Settings Content Styles */
.modern-settings-content {
  padding: 32px;
  height: 100%;
  overflow-y: auto;
}

.settings-section {
  margin-bottom: 40px;
}

.section-header {
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 2px solid #e2e8f0;
}

.section-header .section-title {
  font-size: 20px;
  font-weight: 600;
  color: #2d3748;
  margin: 0 0 8px 0;
}

.section-header .section-description {
  font-size: 14px;
  color: #718096;
  margin: 0;
  line-height: 1.5;
}

.settings-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.setting-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.3s ease;
}

.setting-item:hover {
  border-color: #cbd5e0;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.setting-label {
  display: block;
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 8px;
}

.setting-select,
.setting-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e2e8f0;
  border-radius: 8px;
  font-size: 14px;
  background: white;
  transition: all 0.3s ease;
}

.setting-select:focus,
.setting-input:focus {
  outline: none;
  border-color: #667eea;
  box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.setting-help {
  font-size: 12px;
  color: #718096;
  margin: 8px 0 0 0;
  line-height: 1.4;
}

/* Toggle Switch Styles */
.toggle-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 16px;
}

.toggle-content {
  flex: 1;
}

.toggle-label {
  font-size: 14px;
  font-weight: 600;
  color: #2d3748;
  margin-bottom: 4px;
  display: block;
}

.toggle-description {
  font-size: 12px;
  color: #718096;
  margin: 0;
  line-height: 1.4;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
  flex-shrink: 0;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #cbd5e0;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

input:checked + .toggle-slider {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

/* Settings Footer */
.settings-footer {
  margin-top: 40px;
  padding-top: 24px;
  border-top: 2px solid #e2e8f0;
  display: flex;
  justify-content: flex-end;
}

.save-settings-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 32px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  gap: 8px;
}

.save-settings-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
}

.save-settings-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Loading States */
.settings-loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 300px;
  color: #718096;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #e2e8f0;
  border-top: 4px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}
